using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using CeresMockServer.Options;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;

namespace CeresMockServer.Services;

public class AuthenticationService(IOptions<CeresAuthOptions> options) : IAuthenticationService
{
    private readonly CeresAuthOptions _options = options.Value;

    public Task<(bool Success, string AccessToken, int ExpiresIn)> TryGetTokenAsync(string clientId, string clientSecret, string scope)
    {
        var client = _options.TestClients.FirstOrDefault(c => c.ClientId == clientId && c.ClientSecret == clientSecret);
        if (client is null)
        {
            return Task.FromResult((false, string.Empty, 0));
        }

        var expiresIn = _options.TokenExpirySeconds;
        var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_options.SigningKey));
        var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

        var claims = new List<Claim>
        {
            new(JwtRegisteredClaimNames.Sub, clientId),
            new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new("scope", string.IsNullOrWhiteSpace(scope) ? client.Scope : scope)
        };

        var tokenDescriptor = new JwtSecurityToken(
            issuer: _options.Issuer,
            audience: _options.Audience,
            claims: claims,
            expires: DateTime.UtcNow.AddSeconds(expiresIn),
            signingCredentials: credentials);

        var tokenHandler = new JwtSecurityTokenHandler();
        var token = tokenHandler.WriteToken(tokenDescriptor);

        return Task.FromResult((true, token, expiresIn));
    }
}


namespace CeresTagClient.Core.Exceptions;

public class ApiException : Exception
{
    public int StatusCode { get; }
    public string? ErrorCode { get; }
    public string? ProblemType { get; }
    public string? ProblemTitle { get; }
    public string? ProblemDetail { get; }

    public ApiException(string message, int statusCode, string? errorCode = null, string? problemType = null, string? problemTitle = null, string? problemDetail = null) : base(message)
    {
        StatusCode = statusCode;
        ErrorCode = errorCode;
        ProblemType = problemType;
        ProblemTitle = problemTitle;
        ProblemDetail = problemDetail;
    }
}

public class TagNotFoundException : ApiException
{
    public TagNotFoundException(string esn, string? detail = null, string? type = null)
        : base(detail ?? $"Tag '{esn}' not found", 404, "tag_not_found", type, "tag_not_found", detail) { }
}

public class ValidationFailedException : ApiException
{
    public ValidationFailedException(string message, string? type = null)
        : base(message, 400, "validation_failed", type, "validation_failed", message) { }
}


using System.Net.Http.Headers;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Xunit;

public class DelaySimulationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public DelaySimulationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["CeresMock:ResponseSimulation:DefaultDelayMs"] = "50",
                    ["CeresMock:ResponseSimulation:RandomDelayMax"] = "0",
                    ["CeresMock:ResponseSimulation:BypassHeaderName"] = "X-Bypass-Delay"
                };
                config.AddInMemoryCollection(dict);
            });
        });
    }

    [Fact]
    public async Task AppliesDelayAndSetsHeaders()
    {
        var client = _factory.CreateClient();
        var sw = System.Diagnostics.Stopwatch.StartNew();
        var resp = await client.GetAsync("/healthz");
        sw.Stop();
        resp.EnsureSuccessStatusCode();
        Assert.True(sw.ElapsedMilliseconds >= 40, $"Expected delay >= 40ms, got {sw.ElapsedMilliseconds}");
        Assert.Equal("true", resp.Headers.TryGetValues("X-Delay-Applied", out var vals) ? vals.FirstOrDefault() : null);
    }

    [Fact]
    public async Task BypassHeaderSkipsDelay()
    {
        var client = _factory.CreateClient();

        // Baseline with delay
        var sw1 = System.Diagnostics.Stopwatch.StartNew();
        var baseResp = await client.GetAsync("/healthz");
        sw1.Stop();
        baseResp.EnsureSuccessStatusCode();
        var baseHdr = baseResp.Headers.TryGetValues("X-Delay-Applied", out var baseVals) ? baseVals.FirstOrDefault() : null;
        Assert.Equal("true", baseHdr);

        // With bypass header
        var req = new HttpRequestMessage(HttpMethod.Get, "/healthz");
        req.Headers.Add("X-Bypass-Delay", "true");
        var sw2 = System.Diagnostics.Stopwatch.StartNew();
        var bypassResp = await client.SendAsync(req);
        sw2.Stop();
        bypassResp.EnsureSuccessStatusCode();
        var bypassHdr = bypassResp.Headers.TryGetValues("X-Delay-Applied", out var vals) ? vals.FirstOrDefault() : null;
        Assert.Equal("false", bypassHdr);

        // Bypass should be materially faster than baseline (allowing for noise)
        Assert.True(sw2.ElapsedMilliseconds + 30 <= sw1.ElapsedMilliseconds,
            $"Expected bypass to be at least 30ms faster. Base={sw1.ElapsedMilliseconds}ms, Bypass={sw2.ElapsedMilliseconds}ms");
    }
}


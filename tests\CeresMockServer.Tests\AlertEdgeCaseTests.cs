using System.Net.Http.Headers;
using System.Net.Http.Json;
using CeresMockServer;
using CeresMockServer.Data;
using CeresMockServer.Options;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CeresMockServer.Tests;

public class AlertEdgeCaseTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public AlertEdgeCaseTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["ConnectionStrings:CeresDb"] = $"Data Source=alert-edge-{Guid.NewGuid():N}.db",
                    ["CeresMock:DataGen:PaddockRadiusMeters"] = "1000",
                    ["CeresMock:Features:AlertsEnabled"] = "true"
                };
                config.AddInMemoryCollection(dict);
            });
        });
    }

    private HttpClient CreateAuthClient()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        return client;
    }

    [Fact]
    public async Task Missing_Tag_Returns_404()
    {
        var client = CreateAuthClient();
        var resp = await client.PostAsync("/api/v1/alerts/trigger/high-activity/999999999999999", null);
        Assert.Equal(System.Net.HttpStatusCode.NotFound, resp.StatusCode);
    }

    [Fact]
    public async Task Alerts_Disabled_Returns_503()
    {
        var factory = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, cfg) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["ConnectionStrings:CeresDb"] = $"Data Source=alert-edge2-{Guid.NewGuid():N}.db",
                    ["CeresMock:Features:AlertsEnabled"] = "false"
                };
                cfg.AddInMemoryCollection(dict);
            });
        });
        var client = factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var resp = await client.PostAsync("/api/v1/alerts/trigger/high-activity/100000000000000", null);
        Assert.Equal(System.Net.HttpStatusCode.ServiceUnavailable, resp.StatusCode);
    }

    [Fact]
    public async Task Borderline_Threshold_NoAlert()
    {
        // HighActivityThreshold default is 0.7; create exactly 70% walking
        using var scope = _factory.Services.CreateScope();
        var db = scope.ServiceProvider.GetRequiredService<CeresDbContext>();
        var esn = "700000000000001";
        db.Tags.Add(new Tag { Esn = esn, PropertyId = "p", PropertyName = "P" });
        await db.SaveChangesAsync();

        var now = DateTime.UtcNow;
        var start = now.AddMinutes(-30);
        int total = 31; // inclusive steps to now
        int walking = (int)Math.Round(total * 0.7, MidpointRounding.AwayFromZero);
        int rest = total - walking;
        var t = start;
        for (int i = 0; i < total; i++)
        {
            var activity = i < walking ? "walking" : "resting";
            db.Observations.Add(new Observation { Esn = esn, Timestamp = t, Latitude = -27, Longitude = 151, Activity = activity, Temperature = 20 });
            t = t.AddMinutes(1);
        }
        await db.SaveChangesAsync();

        var eval = scope.ServiceProvider.GetRequiredService<CeresMockServer.Services.IAlertEvaluator>();
        var alerts = await eval.EvaluateAsync(esn);
        Assert.DoesNotContain(alerts, a => a.AlertType == "high_activity");
    }

    [Fact]
    public async Task Empty_Window_NoAlert()
    {
        using var scope = _factory.Services.CreateScope();
        var db = scope.ServiceProvider.GetRequiredService<CeresDbContext>();
        var esn = "700000000000002";
        db.Tags.Add(new Tag { Esn = esn, PropertyId = "p", PropertyName = "P" });
        await db.SaveChangesAsync();

        var eval = scope.ServiceProvider.GetRequiredService<CeresMockServer.Services.IAlertEvaluator>();
        var alerts = await eval.EvaluateAsync(esn);
        Assert.Empty(alerts);
    }
}


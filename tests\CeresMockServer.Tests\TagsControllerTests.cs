using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using CeresMockServer;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

using Microsoft.AspNetCore.Mvc;

namespace CeresMockServer.Tests;

public class TagsControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public TagsControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder => { });
    }

    [Fact]
    public async Task GetDetails_Returns_200_For_Authorized_Valid_Esn()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var seedEsn = "100000000000000";
        var resp = await client.GetAsync($"/api/v1/tags/{seedEsn}");
        Assert.Equal(HttpStatusCode.OK, resp.StatusCode);
        var dto = await resp.Content.ReadFromJsonAsync<TagDetailsResponse>();
        Assert.Equal(seedEsn, dto!.Esn);
    }

    [Fact]
    public async Task GetRecent_Returns_200_With_Pagination_Meta()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var seedEsn = "100000000000000";
        var resp = await client.GetAsync($"/api/v1/tags/{seedEsn}/recent?pageSize=100&pageNumber=1");
        Assert.Equal(HttpStatusCode.OK, resp.StatusCode);
        var dto = await resp.Content.ReadFromJsonAsync<RecentTagDataResponse>();
        Assert.NotNull(dto);
        Assert.NotNull(dto!.Pagination);
        Assert.Equal(100, dto!.Pagination.PageSize);
        Assert.Equal(1, dto!.Pagination.PageNumber);
        Assert.True(dto!.Pagination.TotalCount >= dto!.Data.Count);
    }

    [Fact]
    public async Task GetDetails_InvalidEsn_Returns_400_With_ProblemDetails_Type()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var resp = await client.GetAsync($"/api/v1/tags/12345");
        Assert.Equal(HttpStatusCode.BadRequest, resp.StatusCode);
        Assert.Equal("application/problem+json; charset=utf-8", resp.Content.Headers.ContentType!.ToString());
        var pd = await resp.Content.ReadFromJsonAsync<ProblemDetails>();
        Assert.Equal("validation_failed", pd!.Title);
        Assert.Equal("about:blank", pd.Type);
    }

    [Fact]
    public async Task GetDetails_NotFound_Returns_404_With_ProblemDetails_Type()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var resp = await client.GetAsync($"/api/v1/tags/999999999999999");
        Assert.Equal(HttpStatusCode.NotFound, resp.StatusCode);
        Assert.Equal("application/problem+json; charset=utf-8", resp.Content.Headers.ContentType!.ToString());
        var pd = await resp.Content.ReadFromJsonAsync<ProblemDetails>();
        Assert.Equal("tag_not_found", pd!.Title);
        Assert.Equal("about:blank", pd.Type);
    }

    [Fact]
    public async Task GetRecent_InvalidPagination_Returns_400_With_ProblemDetails_Type()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var seedEsn = "100000000000000";
        var resp = await client.GetAsync($"/api/v1/tags/{seedEsn}/recent?pageSize=0&pageNumber=0");
        Assert.Equal(HttpStatusCode.BadRequest, resp.StatusCode);
        Assert.Equal("application/problem+json; charset=utf-8", resp.Content.Headers.ContentType!.ToString());
        var pd = await resp.Content.ReadFromJsonAsync<ProblemDetails>();
        Assert.Equal("validation_failed", pd!.Title);
        Assert.Equal("about:blank", pd.Type);
    }

}

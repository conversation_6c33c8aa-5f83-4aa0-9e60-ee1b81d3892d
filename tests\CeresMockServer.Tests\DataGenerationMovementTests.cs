using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using CeresMockServer;
using CeresMockServer.Data;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CeresMockServer.Tests;

public class DataGenerationMovementTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public DataGenerationMovementTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["ConnectionStrings:CeresDb"] = $"Data Source=datagen-{Guid.NewGuid():N}.db",
                    ["CeresMock:DataGen:StepSeconds"] = "30"
                };
                config.AddInMemoryCollection(dict);
            });
        });
    }

    [Fact]
    public async Task GenerateMovement_CreatesObservations_WithBehaviorMix()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        // Seed a tag
        using (var scope = _factory.Services.CreateScope())
        {
            var db = scope.ServiceProvider.GetRequiredService<CeresDbContext>();
            db.Tags.Add(new Tag { Esn = "123456789012345", PropertyId = "prop-001", PropertyName = "Test" });
            await db.SaveChangesAsync();
        }

        var start = DateTime.UtcNow.AddHours(-2);
        var end = DateTime.UtcNow.AddHours(-1);
        var resp = await client.PostAsJsonAsync("/api/v1/data-gen/movement", new { Esn = "123456789012345", StartUtc = start, EndUtc = end });
        Assert.Equal(HttpStatusCode.OK, resp.StatusCode);
        var payload = await resp.Content.ReadFromJsonAsync<Dictionary<string,int>>();
        Assert.NotNull(payload);
        Assert.True(payload!["inserted"] > 0);

        // Verify observations exist and contain multiple activities
        using (var scope = _factory.Services.CreateScope())
        {
            var db = scope.ServiceProvider.GetRequiredService<CeresDbContext>();
            var obs = await db.Observations.Where(o => o.Esn == "123456789012345" && o.Timestamp >= start && o.Timestamp <= end).ToListAsync();
            Assert.True(obs.Count > 10);
            var activities = obs.Select(o => o.Activity).Distinct().ToList();
            Assert.Contains("grazing", activities);
            Assert.Contains("walking", activities);
            Assert.Contains("resting", activities);

            // Check coordinates vary (movement) and stay roughly within paddock distance
            var latMin = obs.Min(o => o.Latitude); var latMax = obs.Max(o => o.Latitude);
            var lonMin = obs.Min(o => o.Longitude); var lonMax = obs.Max(o => o.Longitude);
            Assert.NotEqual(latMin, latMax);
            Assert.NotEqual(lonMin, lonMax);
        }
    }
}


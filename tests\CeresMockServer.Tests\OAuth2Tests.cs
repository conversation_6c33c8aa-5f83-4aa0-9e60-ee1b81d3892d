using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc.Testing;
using Xunit;

namespace CeresMockServer.Tests;

public class OAuth2Tests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public OAuth2Tests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(_ => { });
    }

    [Fact]
    public async Task TokenEndpoint_Returns_AccessToken_For_Valid_Client()
    {
        var client = _factory.CreateClient();
        var payload = new { grant_type = "client_credentials", client_id = "test-client-1", client_secret = "test-secret-1" };
        var resp = await client.PostAsJsonAsync("/oauth2/token", payload);
        Assert.Equal(HttpStatusCode.OK, resp.StatusCode);
        var json = await resp.Content.ReadFromJsonAsync<JsonElement>();
        Assert.True(json.TryGetProperty("access_token", out _));
        Assert.Equal("Bearer", json.GetProperty("token_type").GetString());
        Assert.True(json.GetProperty("expires_in").GetInt32() >= 1);
    }

    [Fact]
    public async Task Protected_Endpoints_Require_Token()
    {
        var client = _factory.CreateClient(new WebApplicationFactoryClientOptions { HandleCookies = false });

        // Without token → 401
        var noAuth = await client.GetAsync("/api/v1/tags/100000000000000/recent");
        Assert.Equal(HttpStatusCode.Unauthorized, noAuth.StatusCode);

        // Obtain token
        var payload = new { grant_type = "client_credentials", client_id = "test-client-1", client_secret = "test-secret-1" };
        var tokenResp = await client.PostAsJsonAsync("/oauth2/token", payload);
        Assert.Equal(HttpStatusCode.OK, tokenResp.StatusCode);
        var json = await tokenResp.Content.ReadFromJsonAsync<JsonElement>();
        var token = json.GetProperty("access_token").GetString();

        // With token → 200 OK (even if no data, recent endpoint returns an empty page)
        var authed = new HttpRequestMessage(HttpMethod.Get, "/api/v1/tags/100000000000000/recent");
        authed.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        var ok = await client.SendAsync(authed);
        Assert.Equal(HttpStatusCode.OK, ok.StatusCode);
    }

    [Fact]
    public async Task TokenEndpoint_Rejects_Invalid_Client()
    {
        var client = _factory.CreateClient();
        var payload = new { grant_type = "client_credentials", client_id = "bad", client_secret = "nope" };
        var resp = await client.PostAsJsonAsync("/oauth2/token", payload);
        Assert.Equal(HttpStatusCode.Unauthorized, resp.StatusCode);
    }
}


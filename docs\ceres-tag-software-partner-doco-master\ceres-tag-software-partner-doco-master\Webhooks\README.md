# Webhooks

A webhook delivers data to your solution as it happens (as soon as we receive it). The CERES PORTAL uses webhooks to sent your solution alert data, so that a customer can make a decision on what action to take as soon as possible.

For webhook data to be sent to your solution, you must have a webhook configured and added to the Webhook section of your [CERES PORTAL Software Account](../CeresPortal_SoftwareAccount_UI.md).

When adding a webhook, remember to select the type of data you wish to receive.

You can select historical webhook, alert webhook, Pasture Feed Intake (PFI) webhook or a webhook that handles multiple. The type of webhook you configure depends on how your solution has been set up.

Once you have saved **at least one** webhook, you will see a new section appear in the webhook section that allows you to test the webhook is working. For more details visit [Testing Your Webhook](Testing_Your_Webhook.md)

<kbd><img src="https://user-images.githubusercontent.com/1161583/*********-6e3d1ea6-63fd-4919-b7dc-6bc760e0dd63.png" /></kbd>

Once you have entered your webhook into the Endpoint URL, you can test that it will receive data by pressing the "test" button. This will trigger a sample data packet to be sent with the below structure. 

[{'data':'Test Webhook Please Ignore','webHookType':'Test'}]

<img src="https://github.com/user-attachments/assets/9e9d8605-9dc2-4846-95fa-c6192d93aba5">

In order to pass the test, your webhook has to receive this packet and **respond with a 200 success response** (see example below). This allows our system to know you have recevied the data successfully. 

If a 200 response is not received then the CERES PORTAL will assume you have not recevied the packet and send it again after an incremenetal amount of time up to 6 hours. After **6 hours** of attempting to send the packet, and not receiving a success response, your webhook will be **automatically disabled **. To activate the webhook again you will have to login to your Software Partner account, edit the webhook and update the status to active.  

At set intervals, a notification will be sent to all email addressess assoicated with the Software Partner in CERES PORTAL to notify of webhook **is about to be deactivated**.  Additionally, a notification will be sent when the webhook **has been deactivated**.

<kbd><img src="https://user-images.githubusercontent.com/1161583/*********-488587e6-d8dd-44b8-ac4f-ff64834cad27.png" /></kdb>

## Webhook Security

To ensure that the data being recevied by your webhook is legitimate and definitely from the CERES PORTAL, we have included ```x-hub-signature``` in the header of all data sent to your webhook/s. It contains your **API Client_ID** that has been HMAC SHA256 encrypted. 

This is the **API Client_ID** you generate along with your **API Secret** which is used to access our 0Auth2 RESTful API. For more details on your 0Auth2 RESTful API credentails visit [API Data Discovery](../API_Data_Discovery/README.md)

<img src="https://github.com/user-attachments/assets/ae9edb0b-2346-494c-aabc-f63ca3bc1675" />

An example of how this is done in C# is below:

1. Compute the HMAC using SHA256 (like in the example below in C#)

2. Compare the computed value in the ```x-hub-signature``` header. If the computed HMAC and header value match, then the data was sent from the CERES PORTAL.

    **NOTE** The header starts with the algorithm used followed by the equals sign = and this part will need to be ignored for the comparison (e.g. sha265=sdkfhdf/kgHKkshgSrghdLbnflgnlgj=)

### Example in C#

~~~c#
using Microsoft.AspNetCore.Mvc; 

using System; 

using System.Security.Cryptography; 

using System.Text; 

[ApiController] public class WebhookController : ControllerBase 

{   

​	private const string SecretKey = "your-secret-key";   

​	[HttpPost]   

​	public IActionResult ReceiveWebhook([FromBody] string payload)   

​	{     

​		// Retrieve the signature from the header     

​		if (!Request.Headers.TryGetValue("X-Hub-Signature", out var signatureHeader))     

​		{       

​			return Unauthorized("Missing signature");     

​		}     

​		var receivedSignature = signatureHeader.ToString();     

​		// Compute the HMAC signature     

​		using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(SecretKey));     

​		var computedSignature = "sha256=" + Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes(payload)));

​		// Compare signatures     

​		if (!computedSignature.Equals(receivedSignature, StringComparison.Ordinal))     

​		{       

​			return Unauthorized("Invalid signature");     

​		}     

​		// Process the webhook payload (logging it for this example)     

​		Console.WriteLine("Received valid webhook payload: " + payload);     

​		return Ok("Webhook received");   

​	} 

}
~~~

## Alert Webhook

### Alert Types

There are two alerts that are installed on all CERES TAG devices. Each alert has it's own set of thresholds that must be met in order for the alert data packet to be triggered.

#### activity_threshold_none

A no activity alert is triggered if no activity (via the accelerometer) has been observed on the device for a period of 60 minutes. Once triggered it will continue to send alerts every 12 hours this condition is true.

A no activity alert data packet will have the below structure if sent to postman. You can also trigger this type of alert and test it out yourself. For details visit [Testing Your Webhook](Testing_Your_Webhook.md)

<img src="https://user-images.githubusercontent.com/1161583/232430955-efb31214-8f53-4a22-abeb-08dcfdf25379.png" />

#### activity_threshold_high

A high activity alert is triggered if the activity (via the accelerometer) over a 10 minute period is 9 times greater than normal (average) activity over the last 6 days.

If this high activity continues, you will continue to receive alerts, however this will stop automatically after 6 hours even if the behaviour continues.



Due to the potential frequency of a high activity alert it is necessary to consider how this will be implemented in your customer user interface. Some questions to consider are:

- How often do you notfiy a customer? 

- Under what conditions do you notify a customer? 

- Can a customer determine the conditions under which they are sent a notification?

- Is one device sending an alert in a paddock enough to warrant a notification, or do several animals in the same area need to be affected by high activity?

    

A high activity alert data packet will have the below structure if sent to postman. You can also trigger this type of alert and test it out yourself. For details visit [Testing Your Webhook](Testing_Your_Webhook.md)

<img src="https://user-images.githubusercontent.com/1161583/232430963-3bcc9a81-761b-493f-a3be-89c96b6216ee.png" />



## Historical Webhook

For more details on how this process works and how to test it, visit [Historical Data](../Historical_Data/README.md).



## Pasture Feed Intake (PFI) Webhook

The PFI webhook will be used to send the PFI daily summary packet to a software partner for all PFI enabled devices.

*NOTE 1: All CERES TRACE and CERES RANCH devices purchased after April 19th 2023 are PFI enabled.*

*NOTE 2:* This algorithm was developed **specifically for cattle**. Devices applied to animals other than Cattle will still send through a packet, but the accuracy of the results is not guaranteed.



Following 15 years of R & D and thousands of hours of testing on **Cattle**. The PFI daily summary packet provides a daily update on an individual animal –

- *Walking, Ruminating,* and *Resting* to the nearest minute,

- *Grazing* to the nearest 30 seconds and

- *Drinking* to the nearest 10 seconds

    It also provides a calculation on daily pasture intake in kilograms and methane production in grams.



The data packet sent is calculated for a 24 hour period from midnight local time (based on where the tag is transmitting from) and then transmitted between midnight and 6am local time.

#### PFI Packet Structure

PFI Packet structure varies depending on the *firmware_version* of the device.

You can determine device *firmware_version* via the [GetTagDetailsSince](https://github.com/cerestag/ceres-tag-software-partner-doco/blob/master/API_Data_Discovery/APIDoco.md#get-v1tagdetailsgettagdetailssince).



##### firmware_version 64.4.0.2

* **WebhookType** - "PFI Summary"
* **observationDateUTC** - The UTC datetime the packet was sent
* **esn** - The Electronic Serial Number (ESN) of the tag the packet is for
* **data** - PFI packet data

    * *index 0* - Grazing to the nearest 30 seconds. The number of minutes and seconds spent grazing in the 24 hour period.(e.g. 10 minutes and 30 seconds = 10.5)
    * *index 1* - Resting & Ruminating Minutes. The number of minutes spent resting or ruminating in the 24 hour period.
    * *index 2* - Walking minutes. The number of minutes spent walking in the 24 hour period.
    * *index 3* - Reserved, currently 0 or NULL
    * *index 4* - Reserved, currently 0 or NULL
    * *index 5* - Reserved, currently 0 or NULL
    * *index 6* - Drinking & Unclassified Minutes. The number of minutes spent drinking or unclassified behaviour (Behaviour that does not fit into ny of the other behaviours and has yet to be classified) in the 24 hour period.
    * *index 7* - Dry Matter Intake. Measured in kilograms per day, the amount of dry matter consumed.
    * *index 8* - Methane Production. Measured in grams per day, the amount of methane emitted.



##### firmware_version 64.5.1.2

**For CERES RANCHER Devices, the Resting and Ruminating fields have been separated for the PFI Summary Packet.**

* **WebhookType** - "PFI Summary"
* **observationDateUTC** - The UTC datetime the packet was sent
* **esn** - The Electronic Serial Number (ESN) of the tag the packet is for
* **data** - PFI packet data

    * *index 0* - Grazing to the nearest 30 seconds. The number of minutes and seconds spent grazing in the 24 hour period.(e.g. 10 minutes and 30 seconds = 10.5)
    * *index 1* - Ruminating Minutes. The number of minutes spent ruminating in the 24 hour period.
    * *index 2* - Walking minutes. The number of minutes spent walking in the 24 hour period.
    * *index 3* - Reserved, currently 0 or NULL
    * *index 4* - Resting Minutes. The number of minutes spent resting in the 24 hour period.
    * *index 5* - Reserved, currently 0 or NULL
    * *index 6* - Drinking & Unclassified Minutes. The number of minutes spent drinking or unclassified behaviour (Behaviour that does not fit into ny of the other behaviours and has yet to be classified) in the 24 hour period.
    * *index 7* - Dry Matter Intake. Measured in kilograms per day, the amount of dry matter consumed.
    * *index 8* - Methane Production. Measured in grams per day, the amount of methane emitted.



##### Example Packet Sent through Postman

<img src="https://user-images.githubusercontent.com/1161583/232421177-e230ab5e-76f0-4bbd-b7eb-78f482141f0a.png">


## Standard Webhook

Software Partners can subscribe to receiving the daily standard packets for all CERES TAG devices that the software partner has access to.


#### Standard Data Packet Structure

* **WebhookType** - Standard Packets
* **observationDateUTC** - The UTC datetime the packet was sent
* **esn** - The Electronic Serial Number (ESN) of the tag the packet is for
* **data** - The Standard packet data

    The standard data packet varies based on the different firmware versions. Refer to [FirmwareVersions](../API_Data_Discovery/FirmwareVersion.md) for the data packet information for specific firmware versions of the CERES TAG devices.

##### Example Packet Sent through Postman

<img src="https://github.com/user-attachments/assets/a70a22fe-c4a5-4fbc-b0c1-ef8f739ec272">


## Webhook Testing

There are many different ways to test your webhook properly, but a few examples have been provided below.

### Postman

If you do not have a URL for your webhook, but wish to begin testing and understanding how they work for the CERES PORTAL, you can set one up using the free API platform postman (https://www.postman.com/). For details and screenshots visit [Postman for Webhook Testing](Postman_Webhook_Setup.md)



### Webhook.site

If you do not have a URL for your webhook, but wish to begin testing and see what is sent from the CERES PORTAL, you can temporarily use webhook.site (https://webhook.site/) to receive the webhook data from test. DO NOT USE THIS IN PRODUCTION.


## Example Failed Webhooks

#### Missing https

<kbd><img src="https://user-images.githubusercontent.com/1161583/230308487-322a0c5a-1293-4505-aa13-3d567f5b8b68.png" /></kbd>

#### Endpoint does not exist / 200 response not received

<kbd><img src="https://user-images.githubusercontent.com/1161583/230308513-2176462a-16aa-4619-9045-854e3606473b.png" /></kbd>

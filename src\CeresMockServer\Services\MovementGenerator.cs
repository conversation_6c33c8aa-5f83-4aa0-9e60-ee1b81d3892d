using CeresMockServer.Data;
using CeresMockServer.Options;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace CeresMockServer.Services;

public interface IMovementGenerator
{
    Task<int> GenerateAsync(string esn, DateTime startUtc, DateTime endUtc, CancellationToken ct = default);
}

public class MovementGenerator : IMovementGenerator
{
    private readonly CeresDbContext _db;
    private readonly DataGenOptions _opts;

    public MovementGenerator(CeresDbContext db, IOptions<DataGenOptions> opts)
    {
        _db = db;
        _opts = opts.Value;
    }

    public async Task<int> GenerateAsync(string esn, DateTime startUtc, DateTime endUtc, CancellationToken ct = default)
    {
        if (endUtc <= startUtc) return 0;
        var tag = await _db.Tags.AsNoTracking().SingleOrDefaultAsync(t => t.Esn == esn, ct);
        if (tag == null) return 0;

        // Seed start at center if no recent observation
        var last = await _db.Observations.AsNoTracking().Where(o => o.Esn == esn).OrderByDescending(o => o.Timestamp).FirstOrDefaultAsync(ct);
        double lat = last?.Latitude ?? _opts.DefaultCenterLat;
        double lon = last?.Longitude ?? _opts.DefaultCenterLon;

        int step = Math.Max(5, _opts.StepSeconds);
        var t = startUtc;
        var toInsert = new List<Observation>(capacity: Math.Min(10_000, (int)((endUtc - startUtc).TotalSeconds / step) + 1));

        var rng = Random.Shared;
        while (t <= endUtc)
        {
            // pick behavior
            var r = rng.NextDouble();
            string activity;
            double speed;
            if (r < _opts.GrazingProbability) { activity = "grazing"; speed = _opts.GrazingSpeedMps; }
            else if (r < _opts.GrazingProbability + _opts.TravelingProbability) { activity = "walking"; speed = _opts.TravelingSpeedMps; }
            else { activity = "resting"; speed = _opts.RestingSpeedMps; }

            // random heading, shorter drift for grazing/resting
            var heading = rng.NextDouble() * 2 * Math.PI;
            var meters = speed * step * (activity == "walking" ? 1.0 : 0.3);
            (lat, lon) = MoveMeters(lat, lon, meters, heading);

            // keep within paddock radius by nudging back to center
            var (clat, clon) = (_opts.DefaultCenterLat, _opts.DefaultCenterLon);
            var dist = HaversineMeters(lat, lon, clat, clon);
            if (dist > _opts.PaddockRadiusMeters)
            {
                var backHeading = Math.Atan2(ToRad(clat - lat), ToRad(clon - lon));
                (lat, lon) = MoveMeters(lat, lon, Math.Min(dist - _opts.PaddockRadiusMeters, meters), backHeading);
            }

            // temperature with noise
            var temp = _opts.TemperatureMean + (rng.NextDouble() - 0.5) * 2 * _opts.TemperatureStdDev;

            toInsert.Add(new Observation
            {
                Esn = esn,
                Timestamp = t,
                Latitude = lat,
                Longitude = lon,
                Activity = activity,
                Temperature = temp,
                Altitude = null,
                Hdop = 1.0 + rng.NextDouble()
            });

            t = t.AddSeconds(step);
        }

        if (toInsert.Count > 0)
        {
            await _db.Observations.AddRangeAsync(toInsert, ct);
            var tagToUpdate = await _db.Tags.SingleAsync(x => x.Esn == esn, ct);
            tagToUpdate.LastSeen = endUtc;
            await _db.SaveChangesAsync(ct);
        }
        return toInsert.Count;
    }

    private static (double lat, double lon) MoveMeters(double lat, double lon, double meters, double headingRad)
    {
        if (meters <= 0) return (lat, lon);
        const double EarthRadiusM = 6371000.0;
        var angDist = meters / EarthRadiusM;
        var lat1 = ToRad(lat);
        var lon1 = ToRad(lon);
        var lat2 = Math.Asin(Math.Sin(lat1) * Math.Cos(angDist) + Math.Cos(lat1) * Math.Sin(angDist) * Math.Cos(headingRad));
        var lon2 = lon1 + Math.Atan2(Math.Sin(headingRad) * Math.Sin(angDist) * Math.Cos(lat1), Math.Cos(angDist) - Math.Sin(lat1) * Math.Sin(lat2));
        return (ToDeg(lat2), ToDeg(lon2));
    }

    private static double HaversineMeters(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371000.0;
        var dLat = ToRad(lat2 - lat1);
        var dLon = ToRad(lon2 - lon1);
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) + Math.Cos(ToRad(lat1)) * Math.Cos(ToRad(lat2)) * Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }

    private static double ToRad(double deg) => deg * Math.PI / 180.0;
    private static double ToDeg(double rad) => rad * 180.0 / Math.PI;
}


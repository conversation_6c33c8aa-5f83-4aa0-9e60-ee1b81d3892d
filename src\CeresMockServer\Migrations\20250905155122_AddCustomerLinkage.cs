﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CeresMockServer.Migrations
{
    /// <inheritdoc />
    public partial class AddCustomerLinkage : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CustomerPropertyLinks",
                columns: table => new
                {
                    Id = table.Column<long>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    CustomerId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    PropertyId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    PropertyName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    LinkedAt = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerPropertyLinks", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Customers",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    DisplayName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Customers", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CustomerPropertyLinks_CustomerId_PropertyId",
                table: "CustomerPropertyLinks",
                columns: new[] { "CustomerId", "PropertyId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CustomerPropertyLinks");

            migrationBuilder.DropTable(
                name: "Customers");
        }
    }
}

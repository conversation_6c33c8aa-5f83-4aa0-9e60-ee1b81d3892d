using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using CeresMockServer;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CeresMockServer.Tests;

public class WebhookTypesDeliveryTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    public WebhookTypesDeliveryTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["ConnectionStrings:CeresDb"] = $"Data Source=webhook-types-{Guid.NewGuid():N}.db",
                    ["CeresMock:Webhooks:DeliveryDelayMs"] = "1"
                };
                config.AddInMemoryCollection(dict);
            });
        });
    }

    private HttpClient CreateAuthClient()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        return client;
    }

    [Theory]
    [InlineData("pfi", "/api/v1/webhooks/simulate/pfi")]
    [InlineData("historical", "/api/v1/webhooks/simulate/historical")]
    [InlineData("standard", "/api/v1/webhooks/simulate/standard")]
    public async Task Simulate_Types_Deliver_With_Signature(string type, string pathSim)
    {
        var client = CreateAuthClient();

        // Register webhook pointing to test receiver
        var uniquePath = $"/api/v1/test-webhook/{type}-{Guid.NewGuid():N}";
        var hook = new WebhookCreateRequest
        {
            Url = uniquePath,
            Type = type,
            SharedSecret = "secretsecret",
            Active = true
        };
        var createResp = await client.PostAsJsonAsync("/api/v1/webhooks", hook);
        Assert.Equal(HttpStatusCode.Created, createResp.StatusCode);

        // clear any previous receipts for this path
        await client.DeleteAsync($"/api/v1/test-webhook?path={Uri.EscapeDataString(uniquePath)}");

        var simResp = await client.PostAsync(pathSim, null);
        Assert.Equal(HttpStatusCode.Accepted, simResp.StatusCode);

        for (int i = 0; i < 20; i++)
        {
            var get = await client.GetAsync($"/api/v1/test-webhook/inspect?path={Uri.EscapeDataString(uniquePath)}");
            var items = await get.Content.ReadFromJsonAsync<List<TestItem>>();
            if (items!.Count > 0)
            {
                var item = items![^1];
                Assert.NotNull(item.Headers["X-Ceres-Signature"]);
                Assert.StartsWith("sha256=", item.Headers["X-Ceres-Signature"]!.ToString());
                Assert.NotNull(item.Headers["X-Ceres-Timestamp"]);
                return;
            }
            await Task.Delay(50);
        }

        Assert.True(false, "No webhook received by test endpoint in time");
    }

    private record TestItem(Dictionary<string, string> Headers, string Body);
}


using CeresTagClient.Core.Requests;
using FluentValidation;

namespace CeresTagClient.Application.Validation;

public class HistoricalRequestValidator : AbstractValidator<HistoricalRequest>
{
    public HistoricalRequestValidator()
    {
        RuleFor(x => x.ESN).NotEmpty().Matches("^\\d{15}$");
        RuleFor(x => x.EndDate).GreaterThan(x => x.StartDate);
        RuleFor(x => x).Must(x => (x.EndDate - x.StartDate).TotalDays <= 31)
            .WithMessage("Requested range must not exceed 31 days");
    }
}

public class HistoricalRetrieveRequestValidator : AbstractValidator<HistoricalRetrieveRequest>
{
    public HistoricalRetrieveRequestValidator()
    {
        RuleFor(x => x.ESN).NotEmpty().Matches("^\\d{15}$");
        RuleFor(x => x.RequestId).NotEmpty();
    }
}


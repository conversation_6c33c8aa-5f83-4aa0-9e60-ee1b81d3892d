"use client";
import Link from 'next/link';

const DEMO_ESNS = [
  '500000000000001',
  '500000000000002',
  '500000000000003',
  '500000000000004',
  '500000000000005',
];

export default function TagsList() {
  return (
    <div className="p-4">
      <h1 className="text-xl font-semibold mb-3">Tags</h1>
      <ul className="space-y-2">
        {DEMO_ESNS.map(esn => (
          <li key={esn} className="rounded border border-white/10 p-3 bg-white/5">
            <Link href={`/tags/${esn}`} className="text-[#00D1B2] hover:underline">{esn}</Link>
          </li>
        ))}
      </ul>
    </div>
  );
}


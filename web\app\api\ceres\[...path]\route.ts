import { NextRequest } from 'next/server';

// Simple in-memory token cache (per server process)
let tokenCache: { accessToken: string; expiresAt: number } | null = null;

async function getAccessToken() {
  const baseUrl = process.env.CERES_BASE_URL;
  const clientId = process.env.CERES_CLIENT_ID;
  const clientSecret = process.env.CERES_CLIENT_SECRET;
  if (!baseUrl || !clientId || !clientSecret) {
    throw new Error('Missing CERES_BASE_URL / CERES_CLIENT_ID / CERES_CLIENT_SECRET');
  }

  const now = Date.now();
  if (tokenCache && tokenCache.expiresAt - 120_000 > now && tokenCache.accessToken) {
    return tokenCache.accessToken;
  }

  const resp = await fetch(`${baseUrl}/oauth2/token`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      grant_type: 'client_credentials',
      client_id: clientId,
      client_secret: clientSecret,
    }),
    // Revalidate every call; the endpoint is local
    cache: 'no-store',
  });
  if (!resp.ok) {
    const text = await resp.text();
    throw new Error(`Token request failed: ${resp.status} ${text}`);
  }
  const data = await resp.json();
  tokenCache = {
    accessToken: data.access_token,
    expiresAt: now + (data.expires_in ?? 3600) * 1000,
  };
  return tokenCache.accessToken;
}

export async function GET(req: NextRequest, ctx: { params: Promise<{ path: string[] }> }) {
  const { path } = await ctx.params;
  return proxy(req, path);
}
export async function POST(req: NextRequest, ctx: { params: Promise<{ path: string[] }> }) {
  const { path } = await ctx.params;
  return proxy(req, path);
}
export async function PUT(req: NextRequest, ctx: { params: Promise<{ path: string[] }> }) {
  const { path } = await ctx.params;
  return proxy(req, path);
}
export async function PATCH(req: NextRequest, ctx: { params: Promise<{ path: string[] }> }) {
  const { path } = await ctx.params;
  return proxy(req, path);
}
export async function DELETE(req: NextRequest, ctx: { params: Promise<{ path: string[] }> }) {
  const { path } = await ctx.params;
  return proxy(req, path);
}

async function proxy(req: NextRequest, path: string[]) {
  const baseUrl = process.env.CERES_BASE_URL!;
  const url = new URL(req.url);
  const target = `${baseUrl}/${path.join('/')}${url.search}`;

  const token = await getAccessToken();
  const headers = new Headers(req.headers);
  headers.set('Authorization', `Bearer ${token}`);
  headers.set('Accept', 'application/json');
  // Drop Next.js internal headers
  headers.delete('host');
  headers.delete('x-forwarded-host');
  headers.delete('x-forwarded-proto');

  const init: RequestInit = {
    method: req.method,
    headers,
    body: req.method === 'GET' || req.method === 'HEAD' ? undefined : await req.arrayBuffer(),
    // Avoid Next caching for live data
    cache: 'no-store',
  };

  const resp = await fetch(target, init);
  const body = await resp.arrayBuffer();
  return new Response(body, {
    status: resp.status,
    headers: resp.headers,
  });
}


using System.Text.Json.Serialization;

namespace CeresTagClient.Core.Requests;

public class TagTransferRequestDto
{
    [JsonPropertyName("esns")] public List<string> Esns { get; set; } = new();
    [JsonPropertyName("softwareIdentifier")] public string SoftwareIdentifier { get; set; } = string.Empty;
    [JsonPropertyName("propertySoftwareId")] public string PropertySoftwareId { get; set; } = string.Empty;
    [JsonPropertyName("propertyName")] public string? PropertyName { get; set; }
}


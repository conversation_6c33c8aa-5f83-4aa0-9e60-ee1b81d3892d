using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CeresMockServer.Controllers;

[ApiController]
[Route("api/v1/firstcontact")]
[Produces("application/json")]
public class FirstContactController : ControllerBase
{
    [Authorize]
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public IActionResult Get()
    {
        return Ok(new { status = "ok", server = "CeresMockServer", time = DateTime.UtcNow });
    }
}


using System.Text.Json;

namespace CeresMockServer.Services;

public interface IWebhookPayloadFactory
{
    string CreateAlert();
    string CreatePfi();
    string CreateHistorical();
    string CreateStandard();

    // New: typed alert payload
    string CreateAlertPayload(string esn, string alertType, string severity, DateTime timestamp, double? lat, double? lon, int? battery, object? activitySummary);
}

public class DefaultWebhookPayloadFactory : IWebhookPayloadFactory
{
    public string CreateAlert() => JsonSerializer.Serialize(new { type = "alert", version = "1.0", id = Guid.NewGuid().ToString(), severity = "warning", when = DateTime.UtcNow });
    public string CreatePfi() => JsonSerializer.Serialize(new { type = "pfi", version = "1.0", date = DateTime.UtcNow.Date, pfi = new { score = 0.87, herd = 0.65, individual = 0.92 } });
    public string CreateHistorical() => JsonSerializer.Serialize(new { type = "historical", version = "1.0", request_id = Guid.NewGuid().ToString("N"), status = "ready" });
    public string CreateStandard() => JsonSerializer.Serialize(new { type = "standard", version = "1.0", ts = DateTime.UtcNow, data = new[] { new { esn = "100000000000000", lat = -27.0, lon = 152.0 } } });

    public string CreateAlertPayload(string esn, string alertType, string severity, DateTime timestamp, double? lat, double? lon, int? battery, object? activitySummary)
        => JsonSerializer.Serialize(new
        {
            type = "alert",
            version = "1.0",
            alert_type = alertType,
            esn,
            timestamp,
            severity,
            location = lat.HasValue && lon.HasValue ? new { lat, lon } : null,
            battery_level = battery,
            activity_summary = activitySummary
        });
}


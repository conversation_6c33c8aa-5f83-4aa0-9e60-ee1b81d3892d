namespace CeresTagClient.Core.Interfaces;

public interface ISyncStateRepository
{
    Task<DateTime?> GetLastSyncAsync(string esn, CancellationToken ct = default);
    Task SetLastSyncAsync(string esn, DateTime whenUtc, CancellationToken ct = default);
}

public interface IObservationRepository
{
    Task UpsertBatchAsync(IEnumerable<ObservationEntity> observations, CancellationToken ct = default);
}

public class ObservationEntity
{
    public string Esn { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string Activity { get; set; } = string.Empty;
    public double Temperature { get; set; }
}


using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace CeresMockServer.Swagger;

public class WebhookSimulateOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        // We document a representative example payload that will be delivered to client webhooks
        if (operation == null || context == null) return;
        if (context.ApiDescription.RelativePath is null) return;

        var path = "/" + context.ApiDescription.RelativePath.Trim('/');
        if (!operation.Responses.TryGetValue("202", out var accepted))
        {
            accepted = new OpenApiResponse { Description = "Webhook queued for delivery" };
            operation.Responses["202"] = accepted;
        }

        OpenApiMediaType MakeMedia(string schemaId, string exampleJson)
        {
            return new OpenApiMediaType
            {
                Schema = new OpenApiSchema
                {
                    Reference = new OpenApiReference { Type = ReferenceType.Schema, Id = schemaId }
                },
                Example = OpenApiAnyFactory.CreateFromJson(exampleJson)
            };
        }

        if (path.EndsWith("/api/v1/webhooks/simulate/pfi", StringComparison.OrdinalIgnoreCase))
        {
            accepted.Content["application/json"] = MakeMedia("PfiWebhookPayload", Examples.PfiWebhookExample.Sample);
        }
        else if (path.EndsWith("/api/v1/webhooks/simulate/historical", StringComparison.OrdinalIgnoreCase))
        {
            accepted.Content["application/json"] = MakeMedia("HistoricalWebhookPayload", Examples.HistoricalWebhookExample.Sample);
        }
        else if (path.EndsWith("/api/v1/webhooks/simulate/standard", StringComparison.OrdinalIgnoreCase))
        {
            accepted.Content["application/json"] = MakeMedia("StandardWebhookPayload", Examples.StandardWebhookExample.Sample);
        }
        else if (path.EndsWith("/api/v1/webhooks/simulate/alert", StringComparison.OrdinalIgnoreCase))
        {
            accepted.Content["application/json"] = MakeMedia("AlertWebhookPayload", Examples.AlertWebhookExample.Sample);
        }
    }
}


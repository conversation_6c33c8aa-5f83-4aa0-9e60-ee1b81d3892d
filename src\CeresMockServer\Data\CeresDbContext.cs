using Microsoft.EntityFrameworkCore;

namespace CeresMockServer.Data;

public class CeresDbContext(DbContextOptions<CeresDbContext> options) : DbContext(options)
{
    public DbSet<Tag> Tags => Set<Tag>();
    public DbSet<Observation> Observations => Set<Observation>();
    public DbSet<WebhookRegistration> Webhooks => Set<WebhookRegistration>();
    public DbSet<Customer> Customers => Set<Customer>();
    public DbSet<CustomerPropertyLink> CustomerPropertyLinks => Set<CustomerPropertyLink>();
    public DbSet<Alert> Alerts => Set<Alert>();


    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Tag>(b =>
        {
            b.<PERSON>ey(x => x.Esn);
            b.Property(x => x.Esn).HasMaxLength(15).IsRequired();
            b.Property(x => x.Brand).HasMaxLength(50);
            b.Property(x => x.FirmwareVersion).HasMaxLength(20);
        });

        modelBuilder.Entity<Observation>(b =>
        {
            b.HasKey(x => x.Id);
            b.HasIndex(x => new { x.Esn, x.Timestamp });

        modelBuilder.Entity<WebhookRegistration>(b =>
        {
            b.HasKey(x => x.Id);
            b.Property(x => x.Url).HasMaxLength(500).IsRequired();
            b.Property(x => x.Type).HasMaxLength(50).IsRequired();
            b.Property(x => x.SharedSecret).HasMaxLength(200).IsRequired();
            b.Property(x => x.Active).HasDefaultValue(true);
            b.Property(x => x.Metadata).HasMaxLength(2000);
            b.Property(x => x.CreatedAt).IsRequired();
            b.Property(x => x.UpdatedAt).IsRequired();
            b.HasIndex(x => new { x.Url, x.Type }).IsUnique();
        });

            b.Property(x => x.Esn).HasMaxLength(15).IsRequired();
        });

        modelBuilder.Entity<Customer>(b =>
        {
            b.HasKey(x => x.Id);
            b.Property(x => x.Id).HasMaxLength(100);
            b.Property(x => x.DisplayName).HasMaxLength(200);
        });

        modelBuilder.Entity<CustomerPropertyLink>(b =>
        {
            b.HasKey(x => x.Id);
            b.Property(x => x.CustomerId).HasMaxLength(100).IsRequired();
            b.Property(x => x.PropertyId).HasMaxLength(100).IsRequired();
            b.Property(x => x.PropertyName).HasMaxLength(200);
            b.HasIndex(x => new { x.CustomerId, x.PropertyId }).IsUnique();
        });

        modelBuilder.Entity<Alert>(b =>
        {
            b.HasKey(x => x.Id);
            b.Property(x => x.Esn).HasMaxLength(15).IsRequired();
            b.Property(x => x.AlertType).HasMaxLength(50).IsRequired();
            b.Property(x => x.Severity).HasMaxLength(20).IsRequired();
            b.Property(x => x.Timestamp).IsRequired();
            b.Property(x => x.Metadata).HasMaxLength(4000);
            b.Property(x => x.Acknowledged).HasDefaultValue(false);
            b.HasIndex(x => new { x.Esn, x.Timestamp });
        });

    }
}

public class Tag
{
    public string Esn { get; set; } = string.Empty;
    public string Brand { get; set; } = "CeresTag";
    public string FirmwareVersion { get; set; } = "1.0.0";
    public DateTime ActivationDate { get; set; } = DateTime.UtcNow.AddDays(-30);
    public int BatteryPercentage { get; set; } = 95;
    public string Status { get; set; } = "Active";
    public string PropertyId { get; set; } = "prop-001";
    public string PropertyName { get; set; } = "Test Property";
    public DateTime? LastSeen { get; set; }
}

public class Observation
{
    public long Id { get; set; }
    public string Esn { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string Activity { get; set; } = string.Empty;
    public double Temperature { get; set; }
    public double? Altitude { get; set; }
    public double? Hdop { get; set; }
}

public class Alert
{
    public long Id { get; set; }
    public string Esn { get; set; } = string.Empty;
    public string AlertType { get; set; } = string.Empty; // high_activity, no_activity, geofence_breach, low_battery
    public string Severity { get; set; } = "warning";     // info, warning, critical
    public DateTime Timestamp { get; set; }
    public string? Metadata { get; set; } // JSON
    public bool Acknowledged { get; set; }
}


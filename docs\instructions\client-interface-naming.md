# Client Interface Naming Conventions (Epic 9)

Goal: Ensure strongly-typed client interfaces align with CERES TAG API semantics while preserving backward compatibility.

## Principles
- Interfaces are prefixed with I and grouped by domain: ITagService, IPropertyService, IHistoricalDataService, ITransferService, ICustomerService
- Method names are verb-first, intent-revealing, and map closely to server endpoints
- Backward compatibility is maintained; new names are introduced as extension methods where appropriate
- Use async suffix for asynchronous operations

## Current Interfaces (validated)
- ITagService
  - FirstContactAsync
  - GetTagDetailsAsync
  - GetRecentTagDataAsync
  - RequestHistoricalDataAsync
  - GetHistoricalDataAsync
- IPropertyService
  - GetPropertyLinksAsync
- IHistoricalDataService
  - RequestHistoricalDataAsync (delegates to TagService)
  - GetHistoricalDataAsync (delegates to TagService)
- ITransferService
  - TransferTagsAsync
- ICustomerService
  - StartCustomerAuthAsync
  - ExchangeAuthorizationCodeAsync

## Non-breaking naming improvements
To improve discoverability and alignment with common terminology, the following non-breaking aliases are provided via extension methods:

- ITagService extensions
  - GetRecent<PERSON><PERSON>As<PERSON> (alias for GetRecentTagDataAsync)
  - RequestHistoricalAsync (alias for RequestHistoricalDataAsync)
  - RetrieveHistoricalDataAsync (alias for GetHistoricalDataAsync)

- IPropertyService extensions
  - GetLinksAsync (alias for GetPropertyLinksAsync)

Rationale:
- Required: Aligns with Epic 9’s goal for strongly-typed, clear interfaces
- Safe: Extension methods introduce no breaking changes
- Minimal: Small additions; no service or DI changes required

## Usage example
```csharp
// with DI: var tagService = sp.GetRequiredService<ITagService>();
var recent = await tagService.GetRecentDataAsync("100000000000000", fromDate: DateTime.UtcNow.AddHours(-6));
```

## Future work (optional)
- Consider deduplicating HistoricalDataService by documenting that consumers should prefer ITagService + extensions
- Add xml-doc comments to all public interfaces and DTOs for IntelliSense clarity


# General
*.log
*.tmp
*.temp
*.trx

# OS files
.DS_Store
Thumbs.db

# IDEs
.vs/
.vscode/
.idea/

# Build artifacts
bin/
obj/
Debug/
Release/
[Bb]uild/
TestResults/
**/TestResults/

# User-specific files
*.user
*.rsuser
*.suo
*.userosscache
*.sln.docstates
*.csproj.user

# Packages and caches
packages/
**/node_modules/
.nuget/

# Databases
*.db
*.sqlite
*.db-shm
*.db-wal

# ASP.NET data protection keys (if not intentionally committed)
**/App_Data/

# Rider/JetBrains
*.sln.iml
.idea/

# Coverage
coverage/
*.coverage
*.coveragexml

# Tooling
.tools/
.cache/

# Generated by EF Core (keep Migrations folder, ignore context snapshots if needed)
# (No blanket ignore here to avoid excluding Migrations)


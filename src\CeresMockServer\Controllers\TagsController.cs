using System.Text.RegularExpressions;
using CeresMockServer.Data;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CeresMockServer.Controllers;

[ApiController]
[Authorize]
[Route("api/v1/tags")]
public class TagsController(CeresDbContext db) : ControllerBase
{
    private static readonly Regex EsnRegex = new("^\\d{15}$");

    /// <summary>
    /// Retrieves tag details including metadata and last seen information.
    /// </summary>
    /// <param name="esn">The tag ESN (15 digits).</param>
    /// <response code="200">Returns the tag details.</response>
    /// <response code="400">Validation failed.</response>
    /// <response code="401">Unauthorized.</response>
    /// <response code="404">Tag not found.</response>
    [HttpGet("{esn}")]
    [ProducesResponseType(typeof(TagDetailsResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetDetails([FromRoute] string esn, CancellationToken ct)
    {
        if (!EsnRegex.IsMatch(esn))
            return Problem(title: "validation_failed", detail: "ESN must be exactly 15 numeric digits", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");

        var tag = await db.Tags.AsNoTracking().SingleOrDefaultAsync(t => t.Esn == esn, ct);
        if (tag is null)
            return Problem(title: "tag_not_found", detail: $"Tag '{esn}' not found", statusCode: StatusCodes.Status404NotFound, type: "about:blank");

        var dto = new TagDetailsResponse
        {
            Esn = tag.Esn,
            Brand = tag.Brand,
            FirmwareVersion = tag.FirmwareVersion,
            ActivationDate = tag.ActivationDate,
            BatteryPercentage = tag.BatteryPercentage,
            Status = tag.Status,
            Property = new PropertyDto { Id = tag.PropertyId, Name = tag.PropertyName },
            LastSeen = tag.LastSeen
        };
        return Ok(dto);
    }

    /// <summary>
    /// Retrieves recent observations for the specified tag. Defaults to last 12 hours if fromDate is not provided.
    /// </summary>
    /// <param name="esn">The tag ESN (15 digits).</param>
    /// <param name="fromDate">Optional ISO-8601 timestamp to start from.</param>
    /// <param name="pageSize">Page size (1-1000).</param>
    /// <param name="pageNumber">Page number (>=1).</param>
    /// <response code="200">Returns recent data with pagination metadata.</response>
    /// <response code="400">Validation failed.</response>
    /// <response code="401">Unauthorized.</response>
    [HttpGet("{esn}/recent")]
    [ProducesResponseType(typeof(RecentTagDataResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetRecent([FromRoute] string esn, [FromQuery] DateTime? fromDate, [FromQuery] int pageSize = 100, [FromQuery] int pageNumber = 1, CancellationToken ct = default)
    {
        if (!EsnRegex.IsMatch(esn))
            return Problem(title: "validation_failed", detail: "ESN must be exactly 15 numeric digits", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
        if (pageSize <= 0 || pageSize > 1000 || pageNumber <= 0)
            return Problem(title: "validation_failed", detail: "Invalid pagination parameters", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");

        var start = fromDate ?? DateTime.UtcNow.AddHours(-12);
        var query = db.Observations.AsNoTracking().Where(o => o.Esn == esn && o.Timestamp >= start);
        var total = await query.CountAsync(ct);
        var data = await query
            .OrderBy(o => o.Timestamp)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .Select(o => new RecentObservation
            {
                Esn = o.Esn,
                Timestamp = o.Timestamp,
                Latitude = o.Latitude,
                Longitude = o.Longitude,
                Activity = o.Activity,
                Temperature = o.Temperature,
                Altitude = o.Altitude,
                Hdop = o.Hdop
            })
            .ToListAsync(ct);

        var response = new RecentTagDataResponse
        {
            Data = data,
            Pagination = new PaginationMeta
            {
                TotalCount = total,
                PageSize = pageSize,
                PageNumber = pageNumber,
                HasNextPage = (pageNumber * pageSize) < total
            }
        };

        return Ok(response);
    }


}


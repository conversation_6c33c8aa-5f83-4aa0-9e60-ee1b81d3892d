using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Mvc;

namespace CeresMockServer.Models;

public class TokenRequest
{
    [Required]
    [JsonPropertyName ("grant_type")]
    public string GrantType { get; set; } = string.Empty;

    [Required]    
    [JsonPropertyName("client_id")]
    public string ClientId { get; set; } = string.Empty;

    [Required]
    [JsonPropertyName("client_secret")]
    public string ClientSecret { get; set; } = string.Empty;
}


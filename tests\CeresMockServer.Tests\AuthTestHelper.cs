using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using CeresMockServer.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;

namespace CeresMockServer.Tests;

internal static class AuthTestHelper
{
    public static string CreateTestToken(IConfiguration config, string sub = "test-client-1", string scope = "read write")
    {
        var opts = config.GetSection("CeresMock:Authentication").Get<CeresAuthOptions>()!;
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(opts.SigningKey));
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
        var claims = new[]
        {
            new Claim(JwtRegisteredClaimNames.Sub, sub),
            new Claim("scope", scope)
        };
        var jwt = new JwtSecurityToken(opts.Issuer, opts.Audience, claims, expires: DateTime.UtcNow.AddHours(1), signingCredentials: creds);
        return new JwtSecurityTokenHandler().WriteToken(jwt);
    }
}


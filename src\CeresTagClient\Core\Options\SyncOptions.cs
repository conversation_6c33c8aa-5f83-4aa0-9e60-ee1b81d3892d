namespace CeresTagClient.Core.Options;

public class SyncOptions
{
    // Disabled by default
    public bool Enabled { get; set; } = false;

    // Interval between runs (seconds)
    public int IntervalSeconds { get; set; } = 300; // 5 minutes

    // Page size for API requests
    public int PageSize { get; set; } = 100;

    // Max pages to fetch per ESN per run (safety guard)
    public int MaxPagesPerRun { get; set; } = 10;

    // Batch size for persistence operations
    public int BatchSize { get; set; } = 200;

    // ESNs to synchronize
    public List<string> Esns { get; set; } = new();

    // Default lookback for first run when no sync state exists
    public int InitialLookbackHours { get; set; } = 12;
}


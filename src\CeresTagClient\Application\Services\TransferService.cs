using System.Net.Http.Json;
using CeresTagClient.Core.Dtos;
using CeresTagClient.Core.Interfaces;
using CeresTagClient.Core.Requests;

namespace CeresTagClient.Application.Services;

public class TransferService(IHttpClientFactory factory) : ITransferService
{
    private readonly HttpClient _http = factory.CreateClient("CeresApi");

    public async Task<(IReadOnlyList<string> transferred, IReadOnlyList<(string esn, string reason)> failed)> TransferTagsAsync(
        IEnumerable<string> esns,
        string softwareIdentifier,
        string propertySoftwareId,
        string? propertyName = null,
        CancellationToken ct = default)
    {
        var payload = new TagTransferRequestDto
        {
            Esns = esns.Distinct().ToList(),
            SoftwareIdentifier = softwareIdentifier,
            PropertySoftwareId = propertySoftwareId,
            PropertyName = propertyName
        };

        var resp = await _http.PostAsJsonAsync("api/v1/tags/transfer", payload, ct);
        if ((int)resp.StatusCode == 400)
        {
            var pd = await resp.Content.ReadFromJsonAsync<ProblemDetailsDto>(cancellationToken: ct);
            throw new CeresTagClient.Core.Exceptions.ValidationFailedException(pd?.Detail ?? await resp.Content.ReadAsStringAsync(ct), pd?.Type);
        }
        resp.EnsureSuccessStatusCode();

        var dto = await resp.Content.ReadFromJsonAsync<TagTransferResponseDto>(cancellationToken: ct) ?? new TagTransferResponseDto();
        var failed = dto.Failed.Select(f => (f.Esn, f.Reason)).ToList();
        return (dto.Transferred, failed);
    }
}


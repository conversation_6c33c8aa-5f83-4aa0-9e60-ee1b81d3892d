using System.IdentityModel.Tokens.Jwt;
using System.Text;
using CeresMockServer.Options;
using CeresMockServer.Services;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;

namespace CeresMockServer.Tests;

public class AuthenticationServiceTests
{
    private static IAuthenticationService CreateSut(out CeresAuthOptions opts)
    {
        opts = new CeresAuthOptions
        {
            Issuer = "https://mock.cerestag.com",
            Audience = "ceres-api",
            SigningKey = "unit-test-signing-key-1234567890",
            TokenExpirySeconds = 3600,
            TestClients = [ new() { ClientId = "id", ClientSecret = "secret", Scope = "read write" } ]
        };
        return new AuthenticationService(Microsoft.Extensions.Options.Options.Create(opts));
    }

    [Fact]
    public async Task TryGetTokenAsync_ReturnsToken_ForValidClient()
    {
        var sut = CreateSut(out var opts);
        var (ok, token, exp) = await sut.TryGetTokenAsync("id", "secret", "read");

        Assert.True(ok);
        Assert.NotEmpty(token);
        Assert.Equal(opts.TokenExpirySeconds, exp);

        var handler = new JwtSecurityTokenHandler();
        var jwt = handler.ReadJwtToken(token);
        Assert.Equal("id", jwt.Claims.First(c => c.Type == JwtRegisteredClaimNames.Sub).Value);
        Assert.Equal("read", jwt.Claims.First(c => c.Type == "scope").Value);
        Assert.Equal(opts.Issuer, jwt.Issuer);
        // Validate audience via token validation parameters
        var parameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = opts.Issuer,
            ValidAudience = opts.Audience,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(opts.SigningKey))
        };
        handler.ValidateToken(token, parameters, out _);
    }

    [Fact]
    public async Task TryGetTokenAsync_Fails_ForInvalidClient()
    {
        var sut = CreateSut(out _);
        var (ok, token, exp) = await sut.TryGetTokenAsync("wrong", "creds", "read");

        Assert.False(ok);
        Assert.Equal(string.Empty, token);
        Assert.Equal(0, exp);
    }
}


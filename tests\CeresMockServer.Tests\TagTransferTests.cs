using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using CeresMockServer;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CeresMockServer.Tests;

public class TagTransferTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public TagTransferTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder => { });
    }

    [Fact]
    public async Task Transfer_Succeeds_And_Updates_Tag_Property()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var esn = "100000000000000"; // seeded
        var req = new TagTransferRequest
        {
            Esns = [ esn ],
            SoftwareIdentifier = "client-abc",
            PropertySoftwareId = "prop-xyz",
            PropertyName = "XYZ Farm"
        };

        var resp = await client.PostAsJsonAsync("/api/v1/tags/transfer", req);
        Assert.Equal(HttpStatusCode.OK, resp.StatusCode);
        var result = await resp.Content.ReadFromJsonAsync<TagTransferResult>();
        Assert.Single(result!.Transferred);
        Assert.Empty(result.Failed);

        // Verify change
        var details = await client.GetFromJsonAsync<TagDetailsResponse>($"/api/v1/tags/{esn}");
        Assert.Equal("prop-xyz", details!.Property.Id);
        Assert.Equal("XYZ Farm", details.Property.Name);
    }

    [Fact]
    public async Task Transfer_Partial_Failures_Returns_Mixed_Result()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var existing = "100000000000001";
        var missing = "999999999999998";
        var bad = "123"; // invalid esn
        var req = new TagTransferRequest
        {
            Esns = [ existing, missing, bad ],
            SoftwareIdentifier = "client-abc",
            PropertySoftwareId = "prop-123"
        };

        var resp = await client.PostAsJsonAsync("/api/v1/tags/transfer", req);
        Assert.Equal(HttpStatusCode.OK, resp.StatusCode);
        var result = await resp.Content.ReadFromJsonAsync<TagTransferResult>();
        Assert.Contains(existing, result!.Transferred);
        Assert.Contains(result.Failed, f => f.Esn == missing && f.Reason == "not_found");
        Assert.Contains(result.Failed, f => f.Esn == bad && f.Reason == "invalid_esn");
    }

    [Fact]
    public async Task Transfer_Invalid_Request_Returns_ProblemDetails()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        // Missing required
        var badReq = new TagTransferRequest { Esns = [], SoftwareIdentifier = "", PropertySoftwareId = "" };
        var resp = await client.PostAsJsonAsync("/api/v1/tags/transfer", badReq);
        Assert.Equal(HttpStatusCode.BadRequest, resp.StatusCode);
        Assert.Equal("application/problem+json; charset=utf-8", resp.Content.Headers.ContentType!.ToString());
        var pd = await resp.Content.ReadFromJsonAsync<ProblemDetails>();
        Assert.Equal("validation_failed", pd!.Title);
    }
}


# Software Partner Initiated - Customer Linkage

As a Software Partner, you can now initiate the customer linkage process. The Software Partner Initiated - Customer Linkage integration streamlines the process of linking the CERES TAG PORTAL Company Locations to the associated properties in your software system.

<img src="https://github.com/user-attachments/assets/7f026f2c-55e8-441e-8551-5024eefd8218">

The API is structured into clear steps that guide you from initiating the linkage to managing property associations with your system.
The integration is divided into the following key stages:

1.	Software User Linkage – Generate a URL to redirect users for authentication and access grant.
2.	Software Linkage – Retrieve a list of available and existing properties after the customer completes the linkage flow.
3.	Linking Properties to Software Client Properties – Map CERES TAG PORTAL Company Locations to your properties.

### Step 1: Software User Linkage

To initiate the user linkage process, you need to call the POST /v1/UserLink/Grants/Url endpoint in the Swagger Documentation available at https://extapi.cerestag.com/swagger/index.html. This will return a URL where the customer can authenticate and grant access to their Company Locations. The request requires a ReturnUrl to specify where the user will be redirected once the flow is completed.

After the customer authenticates, they will either select a Company Location or be prompted to create one if no Company Locations exist. If they cancel the flow, they will be sent back to the ReturnUrl with the status set to canceled. If they grant access, a **grantId** will be returned in the URL with the status complete.

<img src="https://github.com/user-attachments/assets/38ed98d6-d0e8-4d97-b528-9dc5e70cdef7">

### Step 2: Software Linkage

Once the user has completed the linkage process, call the GET /v1/UserLink/Grants/Access endpoint with the grantId provided in the previous step. This call returns a list of Company Locations linked to the customer that are available for linkage.

The response will contain details about the existing linked Company Locations and Company Locations that are available for linking, it will also return a **propertyGrantID** which will subsequently be used to map the Company Locations to your software.

<img src="https://github.com/user-attachments/assets/ff346f25-ac0f-4f3c-8daf-6b9287bb3f43">

### Step 3: Linking Properties to Software Client Properties

After receiving a list of available properties, you can map them to your system’s identifiers by calling the POST /v1/UserLink/Grants/AddLinks endpoint. This process will involve linking the CERES TAG PORTAL Company Locations to your software, ensuring that the data is appropriately associated with your system.

You'll need to send a request with the relevant **propertyGrantId** retrieved from the /v1/UserLink/Grants/Access API endpoint and the details of the properties to be linked. This ensures the customer’s Company Locations are properly associated with your software system.

The **identifier** and **displayName** (displayed to the customer) fields are provided by the Software Partner. They **MUST NOT** contain any Personally Identifiable Information(PII). 

<img src="https://github.com/user-attachments/assets/16c02963-e08a-4d4e-8f6f-c78305295d0f">

## Software De-link

To remove the links between your software system and the customer's Company Locations on the CERES TAG PORTAL, use the GET /v1/PropertyLink/Find endpoint. This allows you to search for existing links by specifying the relevant property identifiers. The response will return the **propertySoftwareID**, which you can then use to de-link the Company Locations from your system.

<img src="https://github.com/user-attachments/assets/37698e73-f59d-4522-a5e8-af87cd295312">

You can remove the links by using the POST /v1/PropertyLink/Remove endpoint. This endpoint allows you to unlink the specified Company Locations by providing the propertySoftwareID retrieved earlier in the request. The system will return a list of all successfully unlinked locations, confirming which removals were successful.

<img src="https://github.com/user-attachments/assets/08cc63ac-2ce7-4a68-a3ce-153891fd577e">


# CERES TAG PORTAL Initiated - Customer Linkage

Setting up Customer Linkage allows the CERES PORTAL to automatically provide your software solution with device data for your customers.

Customer Linkage commences ***after***you have completed [API Data Discovery](../API_Data_Disscovery/README.md) and have successfully authenticated and ingested CERES TAG synthetic data into your software solution.

------

## Setting up Customer Linkage

1. A CERES PORTAL software account is created for you in our **TEST Environment**

   To request addtional accounts, email <EMAIL> with the names and emails of the users you'd like added.

2. A workshop session should be arranged to go through your account setup, and run through the details of Customer Linkage. This is also an opportunity to ask questions.

   - To see the steps you will be taken through, visit [Customer Linkage User Interface](CustomerLinkUI.md).

   - To see the data flow steps involved, visit the [Customer Linkage Data Flow Diagrams ](DFD.md).

3. Confirm your API connection details are correct by calling the **TEST** API

   - For details, see **Step 3** in [API Data Discovery](../API_Data_Discovery/README.md) 

4. Configure your application for Customer Linkage using an **0Auth2 Client Credential Flow** and the details entered into your test CERES PORTAL software partner account (image below). For an example see [C# Customer Authentication](CSharpCustomerCredsUserAuthExample.md)

   <img src="https://user-images.githubusercontent.com/1161583/*********-548142f3-fc97-46a4-a9ba-fc5c3096c8f5.png">

   - See [Customer Linkage Data Flow Diagram](DFD.md) for more details on the Data Flow for this step.
   - See [Customer Linkage User Interface](CustomerLinkUI.md) for a visualisation on which variables should go where.
   - Refer to the diagram for **bolded** definitions used below.

   1. Create a HTTPS endpoint, the **Software Partner Customer Authentication Endpoint** in your solution and enter the details into your CERES PORTAL software account in the **Customer Linkage** Section (image above). This endpoint is loaded when a customer goes to verify their account on your system. When launched the CERES PORTAL appends the additional **GET** parameters below (so your endpoint needs to be able to accept these).
      - **response_type**: The type of response expected from the endpoint. This will always be "token" e.g. response_type=token
      - **state**: This is a unique code generated by the CERES PORTAL and used to verify connection requests from system to system. It has a timeout and will change on each request.
      - **client_id**: This is the **CERES PORTAL Auth Client ID** that is specified by you in your CERES PORTAL software account
      -  **redirect_uri**: This is the CERES PORTAL SigninCallback used in the next step
   2.  After a customer successfully signs into your **Software Partner Customer Authentication Endpoint**, your solution must send a **GET** request to the **redirect_uri** (from the previous step) with the below parameters:
      - **token_type**: A Mandatory parameter. The type of token used to authenticate. This will always by "Bearer" e.g. token_type=Bearer
      - **access_token**: A mandatory parameter, this is a <u>JWT token</u> (https://jwt.io/) generated by your software solution that is specific for the customer that just authenticated
      - **state**: A mandatory parameter, this is the <u>state</u> parameter from the previous step. This is a unique code generated by the Ceres Portal and used to verify connection requests from system to system. It has a timeout and will change on each request.
      - **expires_in**: A mandatory parameter that allows you to set a timeout on the request. Typical values is 300.
      - **id_token**: An optional parameter, this is a JWT token</u> (https://jwt.io/) that can be included in your response, but the Ceres Portal system does not do anything with it.
   3. Create a HTTPS endpoint that accepts a **GET** request with your customer's JWT **access_token** as an authorization header ("Authorization", $"Bearer {access_token}"). This endpoint your **Software Partner Customer Account Identifiers Endpoint**. This endpoint authorises using the access_token before returning a relevant List of Identifiers for the customer.
      - The **Software Partner Customer Account Identifiers Endpoint** is saved in the Ceres Portal.
   4. Configure your solution to, upon successful authentication via **Software Partner Customer Account Identifiers Endpoint**, construct a list of account identifiers (farms/locations/accounts) for the customer that exist within your solution.

    - The format of this list is:
      - [
          {"DisplayName" : "Location 1 for customer A", "Identifier" : "UniqueNameForSystem1"},
          {"DisplayName" : "Location 2 for customer A", "Identifier" : "UniqueNameForSystem2"}
        ]
   5. If you wish to have a test customer account created in order to better test this functionality. Please reach out to <NAME_EMAIL> 

**Configuration Examples:**

1. [C# Example Software Partner Customer Authentication](CSharpCustomerCredsUserAuthExample.md)

1. [C# Example Software Partner Customer Account Identifiers](CSharpCustomerCredsUserAcctExample.md)

1. [Customer Linkage Data Flow Diagram](DFD.md)

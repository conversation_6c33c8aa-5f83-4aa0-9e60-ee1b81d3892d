"use client";
import { useEffect, useMemo, useState } from 'react';
import { useRecentMany } from '@/lib/ceres/hooks';

const DEMO_ESNS = [
  '500000000000001',
  '500000000000002',
  '500000000000003',
  '500000000000004',
  '500000000000005',
];

export default function DashboardKpis() {
  const queries = useRecentMany(DEMO_ESNS);

  const activeCount = useMemo(() => queries.filter((q) => q.data?.data?.length).length, [queries]);

  const [batteryAvg, setBatteryAvg] = useState<number | null>(null);

  useEffect(() => {
    // For now, compute fake battery avg from temperature/hints (until we have tag details)
    // This can be updated to fetch details soon.
    const values: number[] = [];
    for (const q of queries) {
      const d = q.data?.data?.[0];
      if (d?.temperature != null) {
        // map temperature roughly to a percentage heuristic for demo
        const pct = Math.max(0, Math.min(100, 110 - d.temperature * 2));
        values.push(pct);
      }
    }
    if (values.length) setBatteryAvg(Math.round(values.reduce((a, b) => a + b, 0) / values.length));
  }, [queries]);

  return (
    <section className="grid grid-cols-2 md:grid-cols-4 gap-3">
      <KpiCard label="Active Tags" value={String(activeCount)} />
      <KpiCard label="Avg Battery" value={batteryAvg != null ? `${batteryAvg}%` : '—'} />
      <KpiCard label="Alerts (24h)" value="—" />
      <KpiCard label="Last Webhook" value="—" />
    </section>
  );
}

function KpiCard({ label, value }: { label: string; value: string }) {
  return (
    <div className="rounded-lg border border-white/10 p-4 bg-white/5">
      <div className="text-xs text-white/60">{label}</div>
      <div className="text-2xl font-semibold mt-1">{value}</div>
    </div>
  );
}


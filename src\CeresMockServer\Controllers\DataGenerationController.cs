using CeresMockServer.Data;
using CeresMockServer.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CeresMockServer.Controllers;

[ApiController]
[Authorize]
[Route("api/v1/data-gen")] 
public class DataGenerationController(CeresDbContext db, IMovementGenerator movement) : ControllerBase
{
    public record GenerateMovementRequest(string Esn, DateTime StartUtc, DateTime EndUtc);

    [HttpPost("movement")] 
    public async Task<IActionResult> GenerateMovement([FromBody] GenerateMovementRequest req, CancellationToken ct)
    {
        if (string.IsNullOrWhiteSpace(req.Esn) || req.EndUtc <= req.StartUtc)
            return BadRequest(new ProblemDetails { Title = "validation_failed", Detail = "Invalid ESN or time range", Status = StatusCodes.Status400BadRequest, Type = "about:blank" });

        var exists = await db.Tags.AsNoTracking().AnyAsync(t => t.Esn == req.Esn, ct);
        if (!exists)
            return NotFound(new ProblemDetails { Title = "tag_not_found", Detail = $"Tag '{req.Esn}' not found", Status = StatusCodes.Status404NotFound, Type = "about:blank" });

        var count = await movement.GenerateAsync(req.Esn, req.StartUtc, req.EndUtc, ct);
        return Ok(new { inserted = count });
    }
}


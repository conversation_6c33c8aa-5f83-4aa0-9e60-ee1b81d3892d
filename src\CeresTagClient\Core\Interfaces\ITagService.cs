using CeresTagClient.Core.Dtos;

namespace CeresTagClient.Core.Interfaces;

public interface ITagService
{
    Task<FirstContactResponse> FirstContactAsync(CancellationToken ct = default);
    Task<TagDetailsResponse> GetTagDetailsAsync(string esn, CancellationToken ct = default);
    Task<RecentTagDataResponse> GetRecentTagDataAsync(string esn, DateTime? fromDate = null, int pageSize = 100, int pageNumber = 1, CancellationToken ct = default);
    Task<HistoricalRequestResponse> RequestHistoricalDataAsync(string esn, DateTime startDate, DateTime endDate, CancellationToken ct = default);
    Task<HistoricalRetrieveResponse> GetHistoricalDataAsync(string esn, string requestId, int pageSize = 100, int pageNumber = 1, CancellationToken ct = default);
}


using System.Text.Json.Serialization;

namespace CeresMockServer.Models;

public class AvailablePropertyDto
{
    [JsonPropertyName("property_id")] public string PropertyId { get; set; } = string.Empty;
    [JsonPropertyName("property_name")] public string PropertyName { get; set; } = string.Empty;
}

public class UserLinkGrantsAccessResponse
{
    // Existing links for the user (reuse CustomerLinkResponse shape)
    [JsonPropertyName("existing_links")] public List<CustomerLinkResponse> ExistingLinks { get; set; } = new();

    // Available properties not yet linked (mock: may be empty)
    [JsonPropertyName("available_links")] public List<AvailablePropertyDto> AvailableLinks { get; set; } = new();
}

public class UserLinkAddLinkItem
{
    [JsonPropertyName("property_id")] public string PropertyId { get; set; } = string.Empty;
    [JsonPropertyName("property_name")] public string PropertyName { get; set; } = string.Empty;
}

public class UserLinkAddLinksRequest
{
    // In the mock, we treat property_grant_id as the customer id
    [JsonPropertyName("property_grant_id")] public string PropertyGrantId { get; set; } = string.Empty;
    [JsonPropertyName("links")] public List<UserLinkAddLinkItem> Links { get; set; } = new();
}

public class FailedLink
{
    [JsonPropertyName("property_id")] public string PropertyId { get; set; } = string.Empty;
    [JsonPropertyName("reason")] public string Reason { get; set; } = string.Empty;
}

public class UserLinkAddLinksResponse
{
    [JsonPropertyName("created")] public List<CustomerLinkResponse> Created { get; set; } = new();
    [JsonPropertyName("failed")] public List<FailedLink> Failed { get; set; } = new();
}


using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace CeresMockServer.Swagger;

public class AlertDocumentFilter : IDocumentFilter
{
    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        // Add a schema for alert webhook payload
        var alertSchema = new OpenApiSchema
        {
            Type = "object",
            Properties = new Dictionary<string, OpenApiSchema>
            {
                ["type"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("alert") },
                ["version"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("1.0") },
                ["alert_type"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("high_activity") },
                ["esn"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("100000000000000") },
                ["timestamp"] = new OpenApiSchema { Type = "string", Format = "date-time" },
                ["severity"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("warning") },
                ["location"] = new OpenApiSchema
                {
                    Type = "object",
                    Nullable = true,
                    Properties = new Dictionary<string, OpenApiSchema>
                    {
                        ["lat"] = new OpenApiSchema { Type = "number", Format = "double" },
                        ["lon"] = new OpenApiSchema { Type = "number", Format = "double" }
                    }
                },
                ["battery_level"] = new OpenApiSchema { Type = "integer", Format = "int32", Nullable = true },
                ["activity_summary"] = new OpenApiSchema
                {
                    Type = "object",
                    Nullable = true,
                    Properties = new Dictionary<string, OpenApiSchema>
                    {
                        ["window_minutes"] = new OpenApiSchema { Type = "integer", Format = "int32" },
                        ["ratio"] = new OpenApiSchema { Type = "number", Format = "double" }
                    }
                }
            },
            Required = new HashSet<string> { "type", "version", "alert_type", "esn", "timestamp", "severity" }
        };

        context.SchemaGenerator.GenerateSchema(typeof(object), context.SchemaRepository);
        swaggerDoc.Components ??= new OpenApiComponents();
        swaggerDoc.Components.Schemas["AlertWebhookPayload"] = alertSchema;

        // Add an example under components for reference
        var example = new OpenApiExample
        {
            Value = OpenApiAnyFactory.CreateFromJson(CeresMockServer.Swagger.Examples.AlertWebhookExample.Sample)
        };
        swaggerDoc.Components.Examples ??= new Dictionary<string, OpenApiExample>();
        swaggerDoc.Components.Examples["AlertWebhookExample"] = example;
    }
}


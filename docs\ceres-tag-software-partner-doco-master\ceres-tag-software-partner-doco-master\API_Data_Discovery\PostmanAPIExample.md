# Postman API Request Example

If you are having trouble figuring out how to query the API endpoint we have included some screenshots below of how to do it using Postman (https://www.postman.com/).

Before being able to generate your access token, you will need a bearer token. For details on how to generate this see [Postman Bearer Token Example](PostmanBearerTokenExample.md) (the bearer token has a default timeout of 3600 seconds)

Once you have your bearer token, you can submit a request as per the configurations below (until the bearer token expires, when you will have to regenerate it)



**NOTE: The example below uses the URL for our TEST environment only, you will have to modify the URL for production when you move across.**

## Header /CheckAuthentication Configuration

<img src="https://user-images.githubusercontent.com/1161583/162162144-806c8924-9ac0-47d6-b912-e8fb30a0608d.png">

## Header /GetTagDetailsSince Configuration

<img src="https://user-images.githubusercontent.com/1161583/192671771-497e0fd7-375b-40b1-8b93-f95ccb53b78f.png">

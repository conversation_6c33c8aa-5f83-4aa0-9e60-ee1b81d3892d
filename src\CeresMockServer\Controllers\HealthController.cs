using CeresMockServer.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CeresMockServer.Controllers;

[ApiController]
[Route("/health")]
public class HealthController(CeresDbContext db) : ControllerBase
{
    [HttpGet]
    public IActionResult Health() => Ok(new { status = "ok" });

    [HttpGet("ready")]
    public async Task<IActionResult> Ready(CancellationToken ct)
    {
        try
        {
            await db.Database.ExecuteSqlRawAsync("SELECT 1", ct);
            return Ok(new { status = "ready" });
        }
        catch
        {
            return StatusCode(StatusCodes.Status503ServiceUnavailable, new { status = "unavailable" });
        }
    }

    [HttpGet("live")]
    public IActionResult Live() => Ok(new { status = "live" });
}


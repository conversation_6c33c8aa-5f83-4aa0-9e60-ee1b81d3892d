import { z } from 'zod';

export const TagDetailsSchema = z.object({
  esn: z.string(),
  brand: z.string().optional(),
  firmwareVersion: z.string().optional(),
  activationDate: z.string().optional(),
  batteryPercentage: z.number().optional(),
  status: z.string().optional(),
  property: z.object({ id: z.string(), name: z.string() }).optional(),
  lastSeen: z.string().optional(),
});
export type TagDetails = z.infer<typeof TagDetailsSchema>;

export const ObservationSchema = z.object({
  esn: z.string(),
  timestamp: z.string(),
  latitude: z.number(),
  longitude: z.number(),
  activity: z.string().optional(),
  temperature: z.number().optional(),
  altitude: z.number().optional(),
  hdop: z.number().optional(),
});
export type Observation = z.infer<typeof ObservationSchema>;

export const RecentDataResponseSchema = z.object({
  data: z.array(ObservationSchema),
  pagination: z.object({ page: z.number(), pageSize: z.number(), totalRecords: z.number() }),
});
export type RecentDataResponse = z.infer<typeof RecentDataResponseSchema>;


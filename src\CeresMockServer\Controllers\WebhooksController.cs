using CeresMockServer.Data;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CeresMockServer.Controllers;

[ApiController]
[Authorize]
[Route("api/v1/webhooks")]
public class WebhooksController(CeresDbContext db) : ControllerBase
{
    [HttpPost]
    [ProducesResponseType(typeof(WebhookResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Create([FromBody] WebhookCreateRequest req, CancellationToken ct)
    {
        // Manual validation rules
        if (string.IsNullOrWhiteSpace(req.Url) || !(Uri.IsWellFormedUriString(req.Url, UriKind.Absolute) || req.Url.StartsWith('/')))
            return Problem(title: "validation_failed", detail: "Url must be an absolute URI or start with '/' for local test endpoints", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
        var allowed = new[] { "alert", "pfi", "historical", "standard" };
        if (string.IsNullOrWhiteSpace(req.Type) || !allowed.Contains(req.Type))
            return Problem(title: "validation_failed", detail: "type must be one of alert|pfi|historical|standard", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
        if (string.IsNullOrWhiteSpace(req.SharedSecret) || req.SharedSecret.Length < 8)
            return Problem(title: "validation_failed", detail: "sharedSecret must be at least 8 characters", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");

        // Enforce unique (Url, Type)
        var exists = await db.Webhooks.AsNoTracking().AnyAsync(w => w.Url == req.Url && w.Type == req.Type, ct);
        if (exists)
            return Problem(title: "validation_failed", detail: "A webhook with the same url and type already exists", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");

        var now = DateTime.UtcNow;
        var entity = new WebhookRegistration
        {
            Url = req.Url,
            Type = req.Type,
            SharedSecret = req.SharedSecret,
            Active = req.Active,
            Metadata = req.Metadata,
            CreatedAt = now,
            UpdatedAt = now
        };
        db.Webhooks.Add(entity);
        await db.SaveChangesAsync(ct);

        var resp = new WebhookResponse
        {
            Id = entity.Id,
            Url = entity.Url,
            Type = entity.Type,
            Active = entity.Active,
            Metadata = entity.Metadata,
            CreatedAt = entity.CreatedAt,
            UpdatedAt = entity.UpdatedAt
        };
        return CreatedAtAction(nameof(GetById), new { id = entity.Id }, resp);
    }

    [HttpGet]
    [ProducesResponseType(typeof(List<WebhookResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> List([FromQuery] string? type, CancellationToken ct)
    {
        var q = db.Webhooks.AsNoTracking().AsQueryable();
        if (!string.IsNullOrWhiteSpace(type))
        {
            var allowed = new[] { "alert", "pfi", "historical", "standard" };
            if (!allowed.Contains(type))
                return Problem(title: "validation_failed", detail: "type must be one of alert|pfi|historical|standard", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
            q = q.Where(w => w.Type == type);
        }

        var items = await q.OrderBy(w => w.Id).Select(w => new WebhookResponse
        {
            Id = w.Id,
            Url = w.Url,
            Type = w.Type,
            Active = w.Active,
            Metadata = w.Metadata,
            CreatedAt = w.CreatedAt,
            UpdatedAt = w.UpdatedAt
        }).ToListAsync(ct);
        return Ok(items);
    }

    [HttpGet("{id:long}")]
    [ProducesResponseType(typeof(WebhookResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetById([FromRoute] long id, CancellationToken ct)
    {
        var w = await db.Webhooks.AsNoTracking().SingleOrDefaultAsync(w => w.Id == id, ct);
        if (w == null)
            return Problem(title: "webhook_not_found", detail: $"Webhook '{id}' not found", statusCode: StatusCodes.Status404NotFound, type: "about:blank");

        return Ok(new WebhookResponse
        {
            Id = w.Id,
            Url = w.Url,
            Type = w.Type,
            Active = w.Active,
            Metadata = w.Metadata,
            CreatedAt = w.CreatedAt,
            UpdatedAt = w.UpdatedAt
        });
    }

    [HttpPut("{id:long}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Update([FromRoute] long id, [FromBody] WebhookUpdateRequest req, CancellationToken ct)
    {
        var w = await db.Webhooks.SingleOrDefaultAsync(w => w.Id == id, ct);
        if (w == null)
            return Problem(title: "webhook_not_found", detail: $"Webhook '{id}' not found", statusCode: StatusCodes.Status404NotFound, type: "about:blank");

        if (req.Url is not null)
        {
            if (!Uri.IsWellFormedUriString(req.Url, UriKind.Absolute))
                return Problem(title: "validation_failed", detail: "Url must be a valid absolute URI", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
            w.Url = req.Url;
        }
        if (req.Type is not null)
        {
            var allowed = new[] { "alert", "pfi", "historical", "standard" };
            if (!allowed.Contains(req.Type))
                return Problem(title: "validation_failed", detail: "type must be one of alert|pfi|historical|standard", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
            w.Type = req.Type;
        }
        if (req.SharedSecret is not null)
        {
            if (req.SharedSecret.Length < 8)
                return Problem(title: "validation_failed", detail: "sharedSecret must be at least 8 characters", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
            w.SharedSecret = req.SharedSecret;
        }
        if (req.Metadata is not null) w.Metadata = req.Metadata;
        if (req.Active.HasValue) w.Active = req.Active.Value;

        // Enforce unique (Url, Type)
        var exists = await db.Webhooks.AsNoTracking().AnyAsync(x => x.Id != id && x.Url == w.Url && x.Type == w.Type, ct);
        if (exists)
            return Problem(title: "validation_failed", detail: "A webhook with the same url and type already exists", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");

        w.UpdatedAt = DateTime.UtcNow;
        await db.SaveChangesAsync(ct);
        return NoContent();
    }

    [HttpDelete("{id:long}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Delete([FromRoute] long id, CancellationToken ct)
    {
        var w = await db.Webhooks.SingleOrDefaultAsync(w => w.Id == id, ct);
        if (w == null)
            return Problem(title: "webhook_not_found", detail: $"Webhook '{id}' not found", statusCode: StatusCodes.Status404NotFound, type: "about:blank");

        db.Webhooks.Remove(w);
        await db.SaveChangesAsync(ct);
        return NoContent();
    }
}


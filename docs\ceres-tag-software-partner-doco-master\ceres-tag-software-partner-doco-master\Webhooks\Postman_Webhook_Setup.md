# Setting up a Webhook using Post<PERSON>

The details below will cover triggering an alert for a Pasture Fedd Intake (PFI) or an Alert webhook. To find out more about how to trigger a [Historical Webhook go here](../Historical_Data/README.md). 



1. Download and install Postman (https://www.postman.com/) for free

      

2. Create a new mock server with an empty GET parameter

    <kbd><img src="https://user-images.githubusercontent.com/1161583/*********-181cbd4a-1a65-4e0f-a33b-43e2fc09877f.png"></kbd>

    

    <kbd><img src="https://user-images.githubusercontent.com/1161583/*********-1588dc5c-6566-44d7-bbc5-cf6e1c895c11.png"></kbd>

    <kbd><img src="https://user-images.githubusercontent.com/1161583/*********-6eb8f3cd-4f01-40b4-854c-c58497fec59c.png"></kbd>

    

3. Copy the new mock server URL and paste and save into the webhook section of your [**CERES PORTAL Software Account**](../CeresPortal_SoftwareAccount_UI.md)

      

    <kbd><img src="https://user-images.githubusercontent.com/1161583/*********-d5ff56b5-e9ec-44c5-9e97-f37c2aabe664.png"></kbd>

    

    <kbd><img src="https://user-images.githubusercontent.com/1161583/*********-fc433ad2-bf3f-4f18-9242-b54bb6e5b8c6.png"></kbd>

    

4. Once saved, a new section "Webhook Test" will appear for you to use to trigger test Alert and Pasture Feed Intake (PFI) webhooks.

    <kbd><img src="https://user-images.githubusercontent.com/1161583/*********-546fc827-2785-443d-9ff6-5d9ca725896b.png"></kbd>

    

5. You will only send a successful test to a device **your software solution has access to** (for more details see [Testing Your Webhook](Testing_Your_Webhook.md)), and check it's received by the postman mock server (NOTE: you may have to refresh the mock postman page)

    1. Trigger Pasture Feed Intake (PFI) Webhook

        <kbd><img src="https://user-images.githubusercontent.com/1161583/*********-********-a76d-429e-b0a8-2cd7a088e2cf.png"></kbd>

        <kbd><img src="https://user-images.githubusercontent.com/1161583/232372149-9853b170-4c5a-46cc-9e14-2d279e481ba4.png"></kbd>

        <kbd><img src="https://user-images.githubusercontent.com/1161583/232372157-ac660135-a2a6-4ada-ba6d-f3a1182224f5.png"></kbd>

    2. Trigger Alert Webhook

        <kbd><img src="https://user-images.githubusercontent.com/1161583/232372369-88a79b86-70ce-4065-8588-d6017bd73f82.png"></kbd>

        <kbd><img src="https://user-images.githubusercontent.com/1161583/232372390-e5055adc-6228-4008-b5a3-8b6a16be37e4.png"></kbd>

        <kbd><img src="https://user-images.githubusercontent.com/1161583/232372405-8cddc40f-76f7-42af-ac03-7070bb78f7d2.png"></kbd>
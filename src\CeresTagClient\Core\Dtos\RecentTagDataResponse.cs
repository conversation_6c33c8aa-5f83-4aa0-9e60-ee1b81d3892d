using System.Text.Json.Serialization;

namespace CeresTagClient.Core.Dtos;

public class RecentTagDataResponse
{
    [JsonPropertyName("data")] public List<RecentObservation> Data { get; set; } = new();
    [JsonPropertyName("pagination")] public PaginationMeta Pagination { get; set; } = new();
}

public class RecentObservation
{
    [JsonPropertyName("esn")] public string Esn { get; set; } = string.Empty;
    [JsonPropertyName("timestamp")] public DateTime Timestamp { get; set; }
    [JsonPropertyName("latitude")] public double Latitude { get; set; }
    [JsonPropertyName("longitude")] public double Longitude { get; set; }
    [JsonPropertyName("activity")] public string Activity { get; set; } = string.Empty;
    [JsonPropertyName("temperature")] public double Temperature { get; set; }
    [JsonPropertyName("altitude")] public double? Altitude { get; set; }
    [JsonPropertyName("hdop")] public double? Hdop { get; set; }
}

public class PaginationMeta
{
    [JsonPropertyName("totalCount")] public int TotalCount { get; set; }
    [JsonPropertyName("pageSize")] public int PageSize { get; set; }
    [JsonPropertyName("pageNumber")] public int PageNumber { get; set; }
    [JsonPropertyName("hasNextPage")] public bool HasNextPage { get; set; }
}


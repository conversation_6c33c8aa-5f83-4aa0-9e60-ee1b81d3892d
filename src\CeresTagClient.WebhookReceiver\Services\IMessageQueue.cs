namespace CeresTagClient.WebhookReceiver.Services;

public record InboundMessage(string Payload, DateTime TimestampUtc);

public interface IMessageQueue
{
    void Enqueue(InboundMessage message);
    bool TryDequeue(out InboundMessage? message);
}

public class InMemoryMessageQueue : IMessageQueue
{
    private readonly System.Collections.Concurrent.ConcurrentQueue<InboundMessage> _queue = new();

    public void Enqueue(InboundMessage message) => _queue.Enqueue(message);

    public bool TryDequeue(out InboundMessage? message)
    {
        var ok = _queue.TryDequeue(out var m);
        message = m;
        return ok;
    }
}


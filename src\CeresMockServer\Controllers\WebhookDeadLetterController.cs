using CeresMockServer.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CeresMockServer.Controllers;

[ApiController]
[Route("api/v1/webhooks/deadletter")]
[Authorize]
public class WebhookDeadLetterController(IWebhookDeadLetterStore store) : ControllerBase
{
    [HttpGet]
    public IActionResult GetAll()
        => Ok(store.GetAll());

    [HttpDelete]
    public IActionResult Clear()
    {
        store.Clear();
        return NoContent();
    }
}


using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CeresTagClient.Core.Dtos;

namespace CeresTagClient.Core.Interfaces;

/// <summary>
/// Non-breaking alias methods for IPropertyService to improve discoverability.
/// </summary>
public static class IPropertyServiceExtensions
{
    /// <summary>
    /// Alias for GetPropertyLinksAsync.
    /// </summary>
    public static Task<IReadOnlyList<CustomerLinkResponse>> GetLinksAsync(this IPropertyService service, string customerId, CancellationToken ct = default)
        => service.GetPropertyLinksAsync(customerId, ct);
}


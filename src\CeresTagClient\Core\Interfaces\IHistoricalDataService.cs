using CeresTagClient.Core.Dtos;

namespace CeresTagClient.Core.Interfaces;

public interface IHistoricalDataService
{
    Task<HistoricalRequestResponse> RequestHistoricalDataAsync(string esn, DateTime startDate, DateTime endDate, CancellationToken ct = default);
    Task<HistoricalRetrieveResponse> GetHistoricalDataAsync(string esn, string requestId, int pageSize = 100, int pageNumber = 1, CancellationToken ct = default);
}


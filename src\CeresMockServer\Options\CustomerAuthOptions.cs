namespace CeresMockServer.Options;

public class CustomerAuthOptions
{
    public string Issuer { get; set; } = "https://mock.cerestag.com/customer";
    public string Audience { get; set; } = "ceres-customer";
    public string SigningKey { get; set; } = "dev-customer-signing-key-change-me";
    public int TokenExpirySeconds { get; set; } = 3600;
    public string DefaultRedirectUri { get; set; } = "http://localhost/callback";
}


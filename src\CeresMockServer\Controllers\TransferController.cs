using System.Text.RegularExpressions;
using CeresMockServer.Data;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CeresMockServer.Controllers;

[ApiController]
[Authorize]
[Route("api/v1/tags/transfer")] // aligns with project routes (mocked analogue of /v1/Tag/Transfer)
public class TransferController(CeresDbContext db) : ControllerBase
{
    private static readonly Regex EsnRegex = new("^\\d{15}$");

    /// <summary>
    /// Transfers tags to a target property. Returns lists of transferred and failed ESNs.
    /// </summary>
    /// <response code="200">Transfer result with success/failure lists.</response>
    /// <response code="400">Validation failed.</response>
    /// <response code="401">Unauthorized.</response>
    [HttpPost]
    [ProducesResponseType(typeof(TagTransferResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Transfer([FromBody] TagTransferRequest request, CancellationToken ct)
    {
        if (request == null)
            return Problem(title: "validation_failed", detail: "Body is required", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
        if (string.IsNullOrWhiteSpace(request.SoftwareIdentifier) || string.IsNullOrWhiteSpace(request.PropertySoftwareId))
            return Problem(title: "validation_failed", detail: "softwareIdentifier and propertySoftwareId are required", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
        if (request.Esns == null || request.Esns.Count == 0)
            return Problem(title: "validation_failed", detail: "esns array is required", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");

        var result = new TagTransferResult();

        foreach (var esn in request.Esns.Distinct())
        {
            if (!EsnRegex.IsMatch(esn))
            {
                result.Failed.Add(new TagTransferFailure { Esn = esn, Reason = "invalid_esn" });
                continue;
            }

            var tag = await db.Tags.FirstOrDefaultAsync(t => t.Esn == esn, ct);
            if (tag is null)
            {
                result.Failed.Add(new TagTransferFailure { Esn = esn, Reason = "not_found" });
                continue;
            }

            // For the mock, assume permissions are satisfied; simply update property fields
            tag.PropertyId = request.PropertySoftwareId;
            tag.PropertyName = request.PropertyName ?? tag.PropertyName;
            result.Transferred.Add(esn);
        }

        if (result.Transferred.Count > 0)
        {
            await db.SaveChangesAsync(ct);
        }

        return Ok(result);
    }
}


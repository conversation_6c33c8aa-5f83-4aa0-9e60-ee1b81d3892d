# API Documentation

> Once you have been given a CERES PORTAL software account in our **TEST Environment** you can access and ingest CERES TAG synthetic data by authenticating and querying our API. If you already have a **TEST** account, you can login [here](https://test.cerestag.com/home).
> 
>Once you have a CERES PORTAL software account and have generated your **API Client_ID & Secret**, you can use the **Swagger API** 



# Authorising the Swagger client

**TEST Swagger API:** [https://extapi.test.cerestag.com/swagger/index.html](https://extapi.test.cerestag.com/swagger/index.html)

Before you can access any data from our API, you have to authenticate using your **API Client_ID & Secret**.

When you visit the TEST Swagger API, select **Authenticate** from the right hand side of the screen.

<img width="162" src="https://user-images.githubusercontent.com/1161583/*********-d061246a-2f4d-4d02-8115-e84acbd121ad.png" />

You will be presented with a pop-up asking for **API Client_ID & Secret**. Enter in these details, select the **Scope** and then select **Authorize**.

<img width="658" alt="Screen Shot 2022-04-07 at 11 43 51 am" src="https://user-images.githubusercontent.com/1161583/*********-498cdc77-c586-4cf7-8197-9759dee818e0.png">

Once you are authorized, you will be able to use the Swagger documentation to test out our endpoints before beginning to implement the data feed into your solution.

# Endpoint Details

## FirstContact

### GET /v1/FirstContact/CheckAuthentication

The endpoint used for first contact to check authentication is working as expected.

<img src="https://user-images.githubusercontent.com/1161583/162160072-4bd22070-7278-4cf6-9a19-6f14dbb61ae9.jpg">

## HistoricalTagData

Allows Software Partners to request and retrieve a complete history of data points for all devices.

For more details see [Historical Data](../Historical_Data/README.md)

### POST /v1/Tags/Historical/Request

This endpoint allows software partners request historical device data.
When a successful call is made our system will queue historical data to be processed.
Once this has finished the webhook endpoint provided by you in your CERES PORTAL account will be called. You will be sent 1 Id per device to retrieve your data.
This can take some time. 
A start date and time in ISO 8601 format can be provided to retrieve data packets from the specified moment onward.  

Any ESN that the software partner doesn't have access to will be passed back in the failed list.

<img src="https://github.com/user-attachments/assets/39434ff0-1c15-496d-b3f0-755e88f07178">

### GET /v1/Tags/Historical/Retrieve

This endpoint allows retrieval of historical data once it is available (when available you are notified via your webhook). You must specify the historyQueryId in your request.

<img src="https://user-images.githubusercontent.com/1161583/*********-380fb68d-f2e4-4979-b3c6-40f0a305d51a.jpg">

## PropertyLink

### GET /v1/PropertyLink/Find

This endpoint allows software partners to look up the software property link by the identifier.

<img src="https://github.com/user-attachments/assets/37698e73-f59d-4522-a5e8-af87cd295312">

### GET /v1/PropertyLink/GetAll

This endpoint allows software partners to look up all the software property links associated with them.

<img src="https://github.com/user-attachments/assets/efa7f364-9051-4f7a-afe3-a799386818a8">

### POST /v1/PropertyLink/Remove

This endpoint allows the software partners to de-link to the mapping of CERES TAG Company Locations and the software partners property.

<img src="https://github.com/user-attachments/assets/08cc63ac-2ce7-4a68-a3ce-153891fd577e">

## RecentTagData

### GET /v1/RecentTagData/GetAnimalObservationsSince

The endpoint allows software partners to get **uptil the last 12 hours** of data for devices associated with their software.
Will return a maximum of 10000 date observations.

#### Request Parameters

**FromDate** (*mandatory*) - A GPS DateTime in iso8601 format e.g 2020-12-18T13:01:01Z OR 2020-12-18T13:01:01+12:00

#### Successful Response

* **observations** - The data packets for each animal, nested within a timetamp.

  * **observationDateUTC** - The timestamp of when the data was captured from the device.

    * **esn** - The ID of the device on the animal. This is the Electronic Serial Number (ESN).

    * **data** - Each packet returned will contain 9 data points. What those data points represent will vary depending on the **TagDetails brand AND *firmwareVersion.*** Your solution will **FIRST** have to determine the brand and firmwareVersion from the TagDetails endpoint, **THEN** interpret the data packet. For all PFI enabled devices, RecentTagData (standard data) packet structure is identical regardless of firmware version. Details of how to interpret the data fields for each combination are outlined in [FirmwareVersions](../API_Data_Discovery/FirmwareVersion.md).
  
  <img src="https://user-images.githubusercontent.com/1161583/*********-5b4acdb4-2b18-4b25-948a-3d996661edcb.jpg">

## Tag

### POST /v1/Tag/Transfer

Use the POST /v1/Tag/Transfer endpoint to transfer devices between a customer's Company Locations. You will need to provide an array of ESNs of the devices to be transferred, along with the softwareIdentifier and propertySoftwareId of the Company Location to transfer these devices into. This will return a list of devices that have successfully been transferred and a list of devices which were failed to transfer.

#### Request Parameters

**esns** - Electronic Serial Number (ESN) of the device
**softwareIdentifier** - Name provided by the Software Partner 
**propertySoftwareID** - Included in the response of the GET /v1/PropertyLink/Find API Endpoint 

#### Response

**moved** - A list of ESNs that were successfully transferred
**failedToMove** - A list of ESNs that couldn’t be transferred, including the reason for the failure

<img src="https://github.com/user-attachments/assets/e547f476-1fff-438c-b25a-ad959f355bed">



## TagDetails

### GET /v1/TagDetails/GetTagDetailsSince

Returns details about devices for customers ([Customer Linkage](../Customer_Linkage/README.md)). 
If no date is set, all devices are returned.
In normal operation, we expect this endpoint would be queried with the timestamp of the last time you queried it (to get changes since then). If there are any new devices associated with your account, they will be returned and records can be created in your system appropriately.

#### Request Parameters

**FromDate** (*optional*) - A GPS DateTime in iso8601 format e.g 2020-12-18T13:01:01Z OR 2020-12-18T13:01:01+12:00
If left blank all devices will return.

**accountIdentifer** (optional) - Filter your results to a single location *identifier*

#### Response

* **propertyTags** - Details of the devices registered against your software
	
	* **identifiers** - The account identifier for the owner of the devices nested in this field. This should be unique ( e.g. HEX, HASH) and contain no identifying information
	  
	  * **tags** - The devices associated with the identifier.
	  	
	  	**Field Definitions**
	  	
	  	
	  	* *esn* - Electronic Serial Number (ESN) of the device, this number is also used in the RecentTagData endpoint
	  	
	  	* *vid* - The Visual ID of the device, written on the outside of the device
	  	
	  	* *rfid* - The RFID of the device if installed
	  	
	  	* *btMac* - The bluetooth MAC address of the device
	  	
	  	* *brand* - The brand of the device. Possible values – CeresTrace, CeresWild, CeresRanch
	  	
	  	* *chargeType* - The type of charge to the software partner. Possible values – Standard, Non charged
	  	
	  	* *linkedDate* - The UTC datetime that the device was linked to it’s current location in the CERES PORTAL. This can be useful in determining if a device has been transferred (so you should get its history) or if it has always been at this location/identifer
	  	
	  	* *firstDate* - The UTC datetime that the first data packet was received for that device. If it is blank when the device has not been activated yet
	  	
	  	* *firmwareVersion* - The version of firmware installed on the device. This should be used to determine what data to expect for the device when retrieving RecentTagData. This information is available at [FirmwareVersions](../API_Data_Discovery/FirmwareVersion.md).
	  	    
	  	    * The version format follows "A.M.m.p" as outlined in the table below
	  	    
	  	        | Value | Description             | Example         |
	  	        | ----- | ----------------------- | --------------- |
	  	        | A     | A per device identifier | CERES WILD = 63 |
	  	        | M     | Device Major version    | 3               |
	  	        | m     | Device minor version    | 0               |
	  	        | p     | Device patch version    | 2               |
	  	
	  	

<img src="https://user-images.githubusercontent.com/1161583/230260962-710408ec-d371-44c5-9db8-6d829032fb4f.jpeg">

## UserAuthentication

### GET /v1/SoftwareVendor/UserAuthentication/SigninCallback

The url_redirect that you are sent to after a customer successfully logs into the **Software Partner Customer Authentication Endpoint**  as a part of [Customer Linkage](../Customer_Linkage/README.md).

### GET /v1/SoftwareVendor/UserAuthentication/UserIdentifiers

An **example** endpoint provided for you to help understand the expected user identifiers response from a software partner system, after the Ceres Portal system successfully makes a request to the **Software Partner Customer Account Identifiers Endpoint**.


## UserLink

### GET /v1/UserLink/Grants/Access

This endpoint returns a list of existing and available property links. It is called with the **grantId** supplied from the /v1/UserLink/Grants/Url endpoint.

<img src="https://github.com/user-attachments/assets/ff346f25-ac0f-4f3c-8daf-6b9287bb3f43">

### POST  /v1/UserLink/Grants/AddLinks

This endpoint allows for the linkage between the software partner property/location to the CERES TAG PORTAL Company Location. It is called with the **propertyGrantId** from the /v1/UserLink/Grants/Access endpoint.

<img src="https://github.com/user-attachments/assets/16c02963-e08a-4d4e-8f6f-c78305295d0f">

### POST /v1/UserLink/Grants/Url

This endpoint generates a URL to redirect the users to for the setup of the the Software Partner Initiated Customer Linkage. 

<img src="https://github.com/user-attachments/assets/38ed98d6-d0e8-4d97-b528-9dc5e70cdef7">
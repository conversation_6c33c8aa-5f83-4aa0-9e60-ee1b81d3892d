using CeresTagClient.Core.Dtos;
using CeresTagClient.Core.Interfaces;

namespace CeresTagClient.Application.Services;

public class HistoricalDataService(ITagService tagService) : IHistoricalDataService
{
    public Task<HistoricalRequestResponse> RequestHistoricalDataAsync(string esn, DateTime startDate, DateTime endDate, CancellationToken ct = default)
        => tagService.RequestHistoricalDataAsync(esn, startDate, endDate, ct);

    public Task<HistoricalRetrieveResponse> GetHistoricalDataAsync(string esn, string requestId, int pageSize = 100, int pageNumber = 1, CancellationToken ct = default)
        => tagService.GetHistoricalDataAsync(esn, requestId, pageSize, pageNumber, ct);
}


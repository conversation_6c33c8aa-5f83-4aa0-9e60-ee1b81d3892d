using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using CeresTagClient.Core.Dtos;
using CeresTagClient.Core.Options;
using CeresTagClient.Infrastructure.Authentication;
using Microsoft.Extensions.Options;

namespace CeresTagClient.Tests;

public class AuthenticationServiceTests
{
    [Fact]
    public async Task GetAccessToken_ReturnsToken_AndCaches()
    {
        // Arrange a fake handler
        var handler = new StubHandler(async req =>
        {
            var response = new TokenResponse { AccessToken = "abc123", ExpiresIn = 7200, TokenType = "Bearer" };
            var msg = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(JsonSerializer.Serialize(response))
            };
            return msg;
        });

        var httpClient = new HttpClient(handler) { BaseAddress = new Uri("https://mock") };        
        var options = Options.Create(new CeresApiOptions
        {
            BaseUrl = "https://mock",
            ClientId = "id",
            ClientSecret = "secret"
        });
        var sut = new AuthenticationService(httpClient, options);

        // Act
        var token1 = await sut.GetAccessTokenAsync();
        var token2 = await sut.GetAccessTokenAsync();

        // Assert
        Assert.Equal("abc123", token1);
        Assert.Equal("abc123", token2); // cached
        Assert.Equal(1, handler.CallCount); // only called once
    }

    private class StubHandler(Func<HttpRequestMessage, Task<HttpResponseMessage>> responder) : HttpMessageHandler
    {
        public int CallCount { get; private set; }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            CallCount++;
            return await responder(request);
        }
    }
}


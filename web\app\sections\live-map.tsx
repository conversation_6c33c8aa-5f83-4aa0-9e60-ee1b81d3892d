"use client";
import { Map, MapOptions, Marker } from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useRecentMany } from '@/lib/ceres/hooks';

// Hardcoded demo ESNs for first iteration
const DEMO_ESNS = [
  '500000000000001',
  '500000000000002',
  '500000000000003',
  '500000000000004',
  '500000000000005',
];

export default function LiveMapSection() {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const mapRef = useRef<Map | null>(null);
  const markersRef = useRef<Record<string, Marker>>({});

  const [styleUrl] = useState(() => process.env.NEXT_PUBLIC_MAP_STYLE || 'https://demotiles.maplibre.org/style.json');

  // Poll recent data per ESN
  const queries = useRecentMany(DEMO_ESNS);
  const recents = useMemo(() => DEMO_ESNS.map((esn, i) => ({ esn, query: queries[i] })), [queries]);

  useEffect(() => {
    if (!containerRef.current || mapRef.current) return;
    const opts: MapOptions = {
      container: containerRef.current,
      style: styleUrl,
      center: [153.0251, -27.4698],
      zoom: 6,
      attributionControl: false,
    };
    const map = new Map(opts);
    mapRef.current = map;

    return () => {
      map.remove();
      mapRef.current = null;
    };
  }, [styleUrl]);

  // Update markers when data comes in
  useEffect(() => {
    const map = mapRef.current;
    if (!map) return;

    for (const { esn, query } of recents) {
      const latest = query.data?.data?.[0];
      if (!latest || latest.longitude === undefined || latest.latitude === undefined) continue;

      let marker = markersRef.current[esn];
      if (!marker) {
        marker = new Marker({ color: '#6C5CE7' }).setLngLat([latest.longitude, latest.latitude]).addTo(map);
        markersRef.current[esn] = marker;
      } else {
        marker.setLngLat([latest.longitude, latest.latitude]);
      }
    }
  }, [recents]);

  return (
    <section className="rounded-lg border border-white/10 p-3 bg-white/5">
      <div className="text-sm text-white/70 mb-2">Live Map</div>
      <div ref={containerRef} className="w-full h-[420px] rounded-md overflow-hidden" />
    </section>
  );
}


using CeresTagClient.Core.Interfaces;
using CeresTagClient.Infrastructure;
using CeresTagClient.WebhookReceiver.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services
builder.Services.AddControllers();

// Bring in client DI to register IWebhookSignatureValidator, Tag services, HttpClients, etc.
builder.Services.AddCeresTagClient(builder.Configuration);

// Local receiver services
builder.Services.AddSingleton<IMessageQueue, InMemoryMessageQueue>();
builder.Services.AddSingleton<IWebhookDeadLetterStore, InMemoryWebhookDeadLetterStore>();
builder.Services.AddSingleton<IWebhookRouter, WebhookRouter>();
builder.Services.AddSingleton<IWebhookHandler, AlertWebhookHandler>();
builder.Services.AddSingleton<IWebhookHandler, PfiWebhookHandler>();
builder.Services.AddSingleton<IWebhookHandler, HistoricalWebhookHandler>();
builder.Services.AddSingleton<IWebhookHandler, StandardWebhookHandler>();
builder.Services.AddHostedService<WebhookBackgroundProcessor>();
// Add optional sync hosted service (respects Sync.Enabled)
builder.Services.AddClientSyncHosted(builder.Configuration);

var app = builder.Build();

// Lightweight health endpoint (no auth)
app.MapGet("/health", () => Results.Json(new { status = "ok" }));

app.MapControllers();

app.Run();

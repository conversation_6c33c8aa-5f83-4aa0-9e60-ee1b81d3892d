using System.Net.Http.Headers;
using System.Net.Http.Json;
using CeresMockServer;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CeresMockServer.Tests;

public class WebhookSchedulerE2ETests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public WebhookSchedulerE2ETests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["ConnectionStrings:CeresDb"] = $"Data Source=scheduler-{Guid.NewGuid():N}.db",
                    ["CeresMock:WebhookSchedule:EnableScheduler"] = "true",
                    ["CeresMock:WebhookSchedule:StandardIntervalMs"] = "50",
                    ["CeresMock:Webhooks:DeliveryDelayMs"] = "0",
                    ["CeresMock:Webhooks:MaxRetries"] = "1",
                    ["CeresMock:Webhooks:RetryBackoffMs"] = "1",
                    ["CeresMock:WebhookFailures:ForceFailure"] = "false"
                };
                config.AddInMemoryCollection(dict);
            });
        });
    }

    [Fact]
    public async Task Scheduler_Enqueues_And_Delivers_To_TestReceiver()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var path = $"/api/v1/test-webhook/scheduler-{Guid.NewGuid():N}";
        var hook = new WebhookCreateRequest { Url = path, Type = "standard", SharedSecret = "sched-secret", Active = true };
        var createResp = await client.PostAsJsonAsync("/api/v1/webhooks", hook);
        createResp.EnsureSuccessStatusCode();

        // clear any previous receipts for this path
        await client.DeleteAsync($"/api/v1/test-webhook?path={Uri.EscapeDataString(path)}");

        // wait for at least one scheduler tick and dispatch
        await Task.Delay(200);

        var inspect = await client.GetAsync($"/api/v1/test-webhook/inspect?path={Uri.EscapeDataString(path)}");
        var items = await inspect.Content.ReadFromJsonAsync<List<object>>();
        Assert.NotNull(items);
        Assert.NotEmpty(items!);
    }
}


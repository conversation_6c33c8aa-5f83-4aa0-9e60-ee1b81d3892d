using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using CeresMockServer;
using CeresMockServer.Data;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CeresMockServer.Tests;

public class AlertWebhookTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public AlertWebhookTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["ConnectionStrings:CeresDb"] = $"Data Source=alertwebhooks-{Guid.NewGuid():N}.db",
                    ["CeresMock:Webhooks:DeliveryDelayMs"] = "1"
                };
                config.AddInMemoryCollection(dict);
            });
        });
    }

    [Fact]
    public async Task Alert_Webhook_Delivered_With_Signature()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        // Register test webhook
        var path = $"/api/v1/test-webhook/alerts-{Guid.NewGuid():N}";
        var hook = new WebhookCreateRequest { Url = path, Type = "alert", SharedSecret = "topsecret", Active = true };
        var createResp = await client.PostAsJsonAsync("/api/v1/webhooks", hook);
        createResp.EnsureSuccessStatusCode();

        // Seed tag and walking-heavy data
        using (var scope = _factory.Services.CreateScope())
        {
            var db = scope.ServiceProvider.GetRequiredService<CeresDbContext>();
            var esn = "300000000000001";
            db.Tags.Add(new Tag { Esn = esn, PropertyId = "p", PropertyName = "P", BatteryPercentage = 50 });
            await db.SaveChangesAsync();
        }

        // Trigger high activity
        var esnParam = "300000000000001";
        var trig = await client.PostAsync($"/api/v1/alerts/trigger/high-activity/{esnParam}", null);
        trig.EnsureSuccessStatusCode();

        // Wait for dispatch
        await Task.Delay(100);

        var inspect = await client.GetAsync($"/api/v1/test-webhook/inspect?path={Uri.EscapeDataString(path)}");
        var items = await inspect.Content.ReadFromJsonAsync<List<dynamic>>();
        Assert.NotNull(items);
        Assert.True(items!.Count > 0);

        var first = items![0];
        JsonElement headers;
        var elem = (JsonElement)first;
        headers = elem.TryGetProperty("headers", out var h1) ? h1 : elem.GetProperty("Headers");
        string? sig = null;
        foreach (var prop in headers.EnumerateObject())
        {
            if (string.Equals(prop.Name, "X-Ceres-Signature", StringComparison.OrdinalIgnoreCase))
            {
                sig = prop.Value.GetString();
                break;
            }
        }
        Assert.False(string.IsNullOrEmpty(sig));
        Assert.StartsWith("sha256=", sig!);

        var bodyElem = elem.TryGetProperty("body", out var b1) ? b1 : elem.GetProperty("Body");
        var body = bodyElem.GetString();
        Assert.NotNull(body);
        var doc = JsonDocument.Parse(body!);
        Assert.Equal("alert", doc.RootElement.GetProperty("type").GetString());
        Assert.Equal("1.0", doc.RootElement.GetProperty("version").GetString());
        Assert.Equal("high_activity", doc.RootElement.GetProperty("alert_type").GetString());
        Assert.Equal("300000000000001", doc.RootElement.GetProperty("esn").GetString());
    }
}


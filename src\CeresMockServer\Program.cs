using CeresMockServer.Options;
using CeresMockServer.Services;
using CeresMockServer.Data;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Serilog configuration
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .CreateLogger();

builder.Host.UseSerilog();

// Options
builder.Services.Configure<CeresAuthOptions>(builder.Configuration.GetSection("CeresMock:Authentication"));
builder.Services.Configure<CustomerAuthOptions>(builder.Configuration.GetSection("CeresMock:CustomerAuth"));
builder.Services.Configure<FeatureOptions>(builder.Configuration.GetSection("CeresMock:Features"));

// Services
// EF Core
builder.Services.AddDbContext<CeresDbContext>(options =>
{
    options.UseSqlite(builder.Configuration.GetConnectionString("CeresDb"));
});

builder.Services.AddSingleton<IAuthenticationService, AuthenticationService>();

// Options
builder.Services.Configure<ResponseSimulationOptions>(builder.Configuration.GetSection("CeresMock:ResponseSimulation"));

builder.Services.AddControllers().ConfigureApiBehaviorOptions(options =>
{
    // Use explicit ProblemDetails responses instead of automatic model state 400s
    options.SuppressModelStateInvalidFilter = true;
});

// CORS (configurable)
var corsOrigins = builder.Configuration.GetSection("CeresMock:Cors:Origins").Get<string[]>() ?? Array.Empty<string>();
var corsPolicy = "ceres-cors";
builder.Services.AddCors(opts =>
{
    opts.AddPolicy(corsPolicy, p =>
    {
        if (corsOrigins.Length == 0)
            p.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod();
        else
            p.WithOrigins(corsOrigins).AllowAnyHeader().AllowAnyMethod();
    });
});

// Simple in-memory rate limiting (lightweight, no new packages)
var rateLimitPerMinute = builder.Configuration.GetValue<int?>("CeresMock:RateLimit:PerMinute") ?? 120;
builder.Services.AddSingleton(new CeresMockServer.RateLimiting.SimpleRateLimiter(rateLimitPerMinute));

// Webhook services
builder.Services.Configure<WebhookOptions>(builder.Configuration.GetSection("CeresMock:Webhooks"));
builder.Services.Configure<WebhookFailureOptions>(builder.Configuration.GetSection("CeresMock:WebhookFailures"));
builder.Services.Configure<WebhookScheduleOptions>(builder.Configuration.GetSection("CeresMock:WebhookSchedule"));

// Data generation options
builder.Services.Configure<DataGenOptions>(builder.Configuration.GetSection("CeresMock:DataGen"));

builder.Services.AddSingleton<IWebhookQueue, InMemoryWebhookQueue>();
builder.Services.AddSingleton<IWebhookPayloadFactory, DefaultWebhookPayloadFactory>();
builder.Services.AddSingleton<IWebhookDeadLetterStore, InMemoryWebhookDeadLetterStore>();
builder.Services.AddSingleton<WebhookFailureRuntime>();
builder.Services.AddHttpClient("webhook");
builder.Services.AddSingleton<IWebhookLocalSink, TestEndpointLocalSink>();
builder.Services.AddScoped<IMovementGenerator, MovementGenerator>();
builder.Services.AddScoped<IAlertEvaluator, AlertEvaluator>();
builder.Services.AddHostedService<WebhookDispatcher>();
builder.Services.AddHostedService<WebhookScheduler>();

// Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo { Title = "CeresMockServer", Version = "v1" });
    var xml = System.IO.Path.Combine(AppContext.BaseDirectory, "CeresMockServer.xml");
    if (System.IO.File.Exists(xml))
        c.IncludeXmlComments(xml, includeControllerXmlComments: true);
    c.DocumentFilter<CeresMockServer.Swagger.AlertDocumentFilter>();
    c.DocumentFilter<CeresMockServer.Swagger.WebhookDocumentFilter>();
    c.OperationFilter<CeresMockServer.Swagger.WebhookSimulateOperationFilter>();
});

// JWT Authentication (token validation for protected endpoints)
var authOptions = builder.Configuration.GetSection("CeresMock:Authentication").Get<CeresAuthOptions>()!;
var key = Encoding.UTF8.GetBytes(authOptions.SigningKey);

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateIssuerSigningKey = true,
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero,
        ValidIssuer = authOptions.Issuer,
        ValidAudience = authOptions.Audience,
        IssuerSigningKey = new SymmetricSecurityKey(key)
    };
});

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    // Initialize database
    using (var scope = app.Services.CreateScope())
    {
        var db = scope.ServiceProvider.GetRequiredService<CeresDbContext>();
        await DbInitializer.InitializeAsync(db);
    }

    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

app.UseCors(corsPolicy);

// Delay simulation (configurable)
app.UseMiddleware<CeresMockServer.Middleware.DelaySimulationMiddleware>();

app.Use(async (ctx, next) =>
{
    var limiter = ctx.RequestServices.GetRequiredService<CeresMockServer.RateLimiting.SimpleRateLimiter>();
    var key = ctx.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    if (!limiter.Allow(key))
    {
        ctx.Response.StatusCode = StatusCodes.Status429TooManyRequests;
        await ctx.Response.WriteAsJsonAsync(new { title = "rate_limited", status = StatusCodes.Status429TooManyRequests, type = "about:blank" });
        return;
    }
    await next();
});

app.UseAuthentication();
app.UseAuthorization();

// Lightweight health endpoint (no auth)
app.MapGet("/healthz", () => Results.Json(new { status = "ok" }));

app.MapControllers();

app.Run();


public partial class Program { }

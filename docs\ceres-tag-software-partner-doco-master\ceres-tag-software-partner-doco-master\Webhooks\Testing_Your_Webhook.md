# Testing your Webhook

This functionality is available in the test environment only. 

Data sent to your webhook for Alerts and Pasture Feed Intake can be triggered manually to allow you to test their functionality and structure.

The details below will cover triggering an alert for a Pasture Feed Intake (PFI) or an Alert webhook. To find out more about how to trigger a [Historical Webhook go here](../Historical_Data/README.md). 

You select the type of webhook you wish to trigger, then enter in the ESN of the device you wish to receive data for (your solution must have access to the device for it to work) and then if you are triggering an Alert, you must select the type of alert you wish to trigger.



To trigger test alerts there is a section called "Webhook Test" on your dashboard that will only display once you have at least one webhook saved and active.

<img src="https://user-images.githubusercontent.com/1161583/232371636-546fc827-2785-443d-9ff6-5d9ca725896b.png">

1. Select the type of webhook you want to trigger from the drop down (make sure the webhook type is selected for your webhook).

<img src="https://user-images.githubusercontent.com/1161583/232372143-95648367-a76d-429e-b0a8-2cd7a088e2cf.png">

2. Enter in the ESN (Electronic Serial Number) of the device you want to trigger the webhook for, ensuring it is a valid ESN. (e.g. 0-8881001) and your software solution has been granted access to data for the device.

<img src="https://user-images.githubusercontent.com/1161583/232372149-9853b170-4c5a-46cc-9e14-2d279e481ba4.png">

3. If you want to trigger an alert, you will also then have to select the type of alert you wish to trigger, before hitting "Send"

    <img src="https://user-images.githubusercontent.com/1161583/232372369-88a79b86-70ce-4065-8588-d6017bd73f82.png">

4. When you click send you will see a message if that it was sent successfully.

<img src="https://user-images.githubusercontent.com/1161583/232372390-e5055adc-6228-4008-b5a3-8b6a16be37e4.png">

4. You should then receive an alert of the type you selected and with the timestamp of when it was submitted to the webhook/s you have saved for that type of webhook (Alert / PFI Summary).

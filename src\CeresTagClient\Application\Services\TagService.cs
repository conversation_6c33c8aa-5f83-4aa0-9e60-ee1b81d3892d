using System.Net.Http.Json;
using CeresTagClient.Core.Dtos;
using CeresTagClient.Core.Exceptions;
using CeresTagClient.Core.Interfaces;
using CeresTagClient.Core.Requests;
using FluentValidation;

namespace CeresTagClient.Application.Services;

public class TagService(IHttpClientFactory factory, IValidator<TagDetailsRequest> tagDetailsValidator, IValidator<RecentTagDataRequest> recentValidator,
    IValidator<HistoricalRequest> historicalValidator, IValidator<HistoricalRetrieveRequest> historicalRetrieveValidator) : ITagService
{
    private readonly HttpClient _http = factory.CreateClient("CeresApi");
    private readonly IValidator<TagDetailsRequest> _tagDetailsValidator = tagDetailsValidator;
    private readonly IValidator<RecentTagDataRequest> _recentValidator = recentValidator;
    private readonly IValidator<HistoricalRequest> _historicalValidator = historicalValidator;
    private readonly IValidator<HistoricalRetrieveRequest> _historicalRetrieveValidator = historicalRetrieveValidator;

    public async Task<FirstContactResponse> FirstContactAsync(CancellationToken ct = default)
    {
        var response = await _http.GetAsync("api/v1/firstcontact", ct);
        response.EnsureSuccessStatusCode();
        var payload = await response.Content.ReadFromJsonAsync<FirstContactResponse>(cancellationToken: ct);
        return payload ?? new FirstContactResponse { Status = "unknown" };
    }

    public async Task<TagDetailsResponse> GetTagDetailsAsync(string esn, CancellationToken ct = default)
    {
        var req = new TagDetailsRequest { ESN = esn };
        var val = await _tagDetailsValidator.ValidateAsync(req, ct);
        if (!val.IsValid)
            throw new ValidationFailedException(string.Join("; ", val.Errors.Select(e => e.ErrorMessage)));

        var response = await _http.GetAsync($"api/v1/tags/{esn}", ct);
        if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            var pd = await response.Content.ReadFromJsonAsync<CeresTagClient.Core.Dtos.ProblemDetailsDto>(cancellationToken: ct);
            throw new TagNotFoundException(esn, pd?.Detail, pd?.Type);
        }
        if ((int)response.StatusCode == 400)
        {
            var pd = await response.Content.ReadFromJsonAsync<CeresTagClient.Core.Dtos.ProblemDetailsDto>(cancellationToken: ct);
            throw new ValidationFailedException(pd?.Detail ?? await response.Content.ReadAsStringAsync(ct), pd?.Type);
        }
        response.EnsureSuccessStatusCode();
        var payload = await response.Content.ReadFromJsonAsync<TagDetailsResponse>(cancellationToken: ct);
        return payload ?? new TagDetailsResponse { Esn = esn };
    }

    public async Task<RecentTagDataResponse> GetRecentTagDataAsync(string esn, DateTime? fromDate = null, int pageSize = 100, int pageNumber = 1, CancellationToken ct = default)
    {
        var req = new RecentTagDataRequest { ESN = esn, FromDate = fromDate, PageSize = pageSize, PageNumber = pageNumber };
        var val = await _recentValidator.ValidateAsync(req, ct);
        if (!val.IsValid)
            throw new ValidationFailedException(string.Join("; ", val.Errors.Select(e => e.ErrorMessage)));

        var url = $"api/v1/tags/{esn}/recent?pageSize={pageSize}&pageNumber={pageNumber}" + (fromDate.HasValue ? $"&fromDate={fromDate.Value:O}" : "");
        var response = await _http.GetAsync(url, ct);
        if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            var pd = await response.Content.ReadFromJsonAsync<CeresTagClient.Core.Dtos.ProblemDetailsDto>(cancellationToken: ct);
            throw new TagNotFoundException(esn, pd?.Detail, pd?.Type);
        }
        if ((int)response.StatusCode == 400)
        {
            var pd = await response.Content.ReadFromJsonAsync<CeresTagClient.Core.Dtos.ProblemDetailsDto>(cancellationToken: ct);
            throw new ValidationFailedException(pd?.Detail ?? await response.Content.ReadAsStringAsync(ct), pd?.Type);
        }
        response.EnsureSuccessStatusCode();
        var payload = await response.Content.ReadFromJsonAsync<RecentTagDataResponse>(cancellationToken: ct);
        return payload ?? new RecentTagDataResponse();
    }

    public async Task<HistoricalRequestResponse> RequestHistoricalDataAsync(string esn, DateTime startDate, DateTime endDate, CancellationToken ct = default)
    {
        var req = new HistoricalRequest { ESN = esn, StartDate = startDate, EndDate = endDate };
        var val = await _historicalValidator.ValidateAsync(req, ct);
        if (!val.IsValid)
            throw new ValidationFailedException(string.Join("; ", val.Errors.Select(e => e.ErrorMessage)));

        var payload = new { startDate = startDate, endDate = endDate };
        var response = await _http.PostAsJsonAsync($"api/v1/tags/{esn}/historical/request", payload, ct);
        if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            var pd = await response.Content.ReadFromJsonAsync<ProblemDetailsDto>(cancellationToken: ct);
            throw new TagNotFoundException(esn, pd?.Detail, pd?.Type);
        }
        if ((int)response.StatusCode == 400)
        {
            var pd = await response.Content.ReadFromJsonAsync<ProblemDetailsDto>(cancellationToken: ct);
            throw new ValidationFailedException(pd?.Detail ?? await response.Content.ReadAsStringAsync(ct), pd?.Type);
        }
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadFromJsonAsync<HistoricalRequestResponse>(cancellationToken: ct);
        return result ?? new HistoricalRequestResponse { RequestId = string.Empty };
    }

    public async Task<HistoricalRetrieveResponse> GetHistoricalDataAsync(string esn, string requestId, int pageSize = 100, int pageNumber = 1, CancellationToken ct = default)
    {
        var req = new HistoricalRetrieveRequest { ESN = esn, RequestId = requestId };
        var val = await _historicalRetrieveValidator.ValidateAsync(req, ct);
        if (!val.IsValid)
            throw new ValidationFailedException(string.Join("; ", val.Errors.Select(e => e.ErrorMessage)));

        var response = await _http.GetAsync($"api/v1/tags/{esn}/historical/{requestId}?pageSize={pageSize}&pageNumber={pageNumber}", ct);
        if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            var pd = await response.Content.ReadFromJsonAsync<ProblemDetailsDto>(cancellationToken: ct);
            throw new TagNotFoundException(esn, pd?.Detail, pd?.Type);
        }
        if ((int)response.StatusCode == 400)
        {
            var pd = await response.Content.ReadFromJsonAsync<ProblemDetailsDto>(cancellationToken: ct);
            throw new ValidationFailedException(pd?.Detail ?? await response.Content.ReadAsStringAsync(ct), pd?.Type);
        }
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadFromJsonAsync<HistoricalRetrieveResponse>(cancellationToken: ct);
        return result ?? new HistoricalRetrieveResponse { Status = "unknown" };
    }
}


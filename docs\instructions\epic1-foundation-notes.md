# Epic 1: Mock Server Foundation & Infrastructure - Implementation Notes

This document summarizes configuration and endpoints added/validated as part of Epic 1.

## Health Endpoints
- GET /health → { status: "ok" }
- GET /health/live → { status: "live" }
- GET /health/ready → { status: "ready" } if DB reachable; 503 otherwise

Notes:
- Liveness returns immediately without dependencies.
- Readiness performs a light DB check (SELECT 1) against SQLite via EF Core.

## CORS Configuration
- Config key: CeresMock:Cors:Origins
  - When empty or missing: AllowAnyOrigin/AnyHeader/AnyMethod (development-friendly)
  - When provided: The specified origins are allowed; headers/methods are allowed.

Example appsettings override:
{
  "CeresMock": {
    "Cors": {
      "Origins": ["https://example.com", "http://localhost:5173"]
    }
  }
}

## Rate Limiting
- Lightweight in-memory limiter (no extra dependencies)
- Config key: CeresMock:RateLimit:PerMinute (default 120)
- Per-remote-IP sliding window by minute
- 429 Too Many Requests is returned with a small problem object

Example appsettings override:
{
  "CeresMock": {
    "RateLimit": {
      "PerMinute": 60
    }
  }
}

## Logging & Monitoring
- Serilog is configured to read from appsettings and write to Console.
- Enriched with log context; request logs emitted by ASP.NET Core and our controllers.
- Health endpoints are included in logs for visibility.

Basic Serilog settings in appsettings:
{
  "Serilog": {
    "MinimumLevel": "Information",
    "WriteTo": [ { "Name": "Console" } ]
  }
}

## Configuration Management
- Options are bound using IOptions pattern for:
  - CeresMock:Authentication
  - CeresMock:CustomerAuth
  - CeresMock:Features
  - CeresMock:Webhooks, WebhookFailures, WebhookSchedule
  - CeresMock:DataGen
  - CeresMock:Cors (Origins)
  - CeresMock:RateLimit (PerMinute)
- ConnectionStrings:CeresDb points to SQLite file (EnsureCreated used; no automatic migrations applied).

## Test Coverage Added
- Health endpoints integration tests
- CORS preflight behavior with configured origins (header presence)
- Rate limiting behavior (deterministic limit via service replacement in test)

## Backward Compatibility
- No changes to alert endpoints
- Webhook endpoints remain backward compatible
- No database migrations introduced; EnsureCreated retained


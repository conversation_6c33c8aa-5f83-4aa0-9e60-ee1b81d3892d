using Microsoft.Extensions.Options;

namespace CeresMockServer.Services;

public class WebhookFailureRuntime : IOptionsSnapshot<CeresMockServer.Options.WebhookFailureOptions>
{
    private readonly object _lock = new();
    private Options.WebhookFailureOptions _current;
    public Options.WebhookFailureOptions Get(string? name) => _current;

    public WebhookFailureRuntime(IOptions<Options.WebhookFailureOptions> initial)
    {
        _current = new Options.WebhookFailureOptions
        {
            ForceFailure = initial.Value.ForceFailure,
            RandomFailurePercent = initial.Value.RandomFailurePercent
        };
    }

    public Options.WebhookFailureOptions Value => _current;

    public void Set(bool? forceFailure = null, int? randomPercent = null)
    {
        lock (_lock)
        {
            _current = new Options.WebhookFailureOptions
            {
                ForceFailure = forceFailure ?? _current.ForceFailure,
                RandomFailurePercent = randomPercent ?? _current.RandomFailurePercent
            };
        }
    }
}


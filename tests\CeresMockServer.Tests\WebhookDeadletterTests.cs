using System.Net.Http.Headers;
using System.Net.Http.Json;
using CeresMockServer;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;

namespace CeresMockServer.Tests;

public class WebhookDeadletterTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    public WebhookDeadletterTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["ConnectionStrings:CeresDb"] = $"Data Source=webhook-deadletters-{Guid.NewGuid():N}.db",
                    ["CeresMock:Webhooks:DeliveryDelayMs"] = "1",
                    ["CeresMock:Webhooks:MaxRetries"] = "2",
                    ["CeresMock:Webhooks:RetryBackoffMs"] = "5",
                    ["CeresMock:WebhookFailures:ForceFailure"] = "true"
                };
                config.AddInMemoryCollection(dict);
            });
        });
    }

    private HttpClient CreateAuthClient()
    {
        var client = _factory.CreateClient();
        var config = _factory.Services.GetService(typeof(IConfiguration)) as IConfiguration;
        var token = AuthTestHelper.CreateTestToken(config!);
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        return client;
    }

    [Theory]
    [InlineData("pfi", "/api/v1/webhooks/simulate/pfi")]
    [InlineData("historical", "/api/v1/webhooks/simulate/historical")]
    [InlineData("standard", "/api/v1/webhooks/simulate/standard")]
    public async Task Failing_Webhooks_End_Up_In_Deadletter(string type, string simulatePath)
    {
        var client = CreateAuthClient();

        var hook = new WebhookCreateRequest
        {
            Url = "http://localhost:65530/nonexistent",
            Type = type,
            SharedSecret = "deadlettersecret",
            Active = true
        };
        var createResp = await client.PostAsJsonAsync("/api/v1/webhooks", hook);
        createResp.EnsureSuccessStatusCode();

        var clear = await client.DeleteAsync("/api/v1/webhooks/deadletter");
        clear.EnsureSuccessStatusCode();

        var simResp = await client.PostAsync(simulatePath, null);
        simResp.EnsureSuccessStatusCode();

        // Give time for retries and deadletter
        await Task.Delay(500);

        var get = await client.GetAsync("/api/v1/webhooks/deadletter");
        var items = await get.Content.ReadFromJsonAsync<List<dynamic>>();
        Assert.NotNull(items);
        Assert.True(items!.Count > 0);
        var last = items![0];
        Assert.Equal(type, (string)last.GetProperty("type").GetString()!);
    }
}


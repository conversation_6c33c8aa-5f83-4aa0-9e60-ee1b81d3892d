using CeresTagClient.Core.Dtos.Webhooks;
using CeresTagClient.Core.Interfaces;

namespace CeresTagClient.Application.Webhooks;

public class NoopAlertHandler : IWebhookEventHandler<AlertWebhookDto>
{
    public Task HandleAsync(AlertWebhookDto payload, CancellationToken ct = default) => Task.CompletedTask;
}

public class NoopPfiHandler : IWebhookEventHandler<PfiWebhookDto>
{
    public Task HandleAsync(PfiWebhookDto payload, CancellationToken ct = default) => Task.CompletedTask;
}

public class NoopHistoricalHandler : IWebhookEventHandler<HistoricalWebhookDto>
{
    public Task HandleAsync(HistoricalWebhookDto payload, CancellationToken ct = default) => Task.CompletedTask;
}

public class NoopStandardHandler : IWebhookEventHandler<StandardWebhookDto>
{
    public Task HandleAsync(StandardWebhookDto payload, CancellationToken ct = default) => Task.CompletedTask;
}


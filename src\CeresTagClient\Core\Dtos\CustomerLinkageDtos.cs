using System.Text.Json.Serialization;

namespace CeresTagClient.Core.Dtos;

public class CustomerAuthStartResponse
{
    [JsonPropertyName("authorization_url")] public string AuthorizationUrl { get; set; } = string.Empty;
    [JsonPropertyName("state")] public string State { get; set; } = string.Empty;
}

public class CustomerAuthCallbackRequestDto
{
    [JsonPropertyName("code")] public string Code { get; set; } = string.Empty;
    [JsonPropertyName("state")] public string State { get; set; } = string.Empty;
}

public class CustomerTokenResponse
{
    [JsonPropertyName("access_token")] public string AccessToken { get; set; } = string.Empty;
    [JsonPropertyName("expires_in")] public int ExpiresIn { get; set; }
    [JsonPropertyName("customer_id")] public string CustomerId { get; set; } = string.Empty;
}

public class CustomerLinkResponse
{
    [JsonPropertyName("id")] public long Id { get; set; }
    [JsonPropertyName("customer_id")] public string CustomerId { get; set; } = string.Empty;
    [JsonPropertyName("property_id")] public string PropertyId { get; set; } = string.Empty;
    [JsonPropertyName("property_name")] public string PropertyName { get; set; } = string.Empty;
    [JsonPropertyName("linked_at")] public DateTime LinkedAt { get; set; }
}


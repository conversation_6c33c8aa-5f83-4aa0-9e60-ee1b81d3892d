using Microsoft.EntityFrameworkCore;

namespace CeresMockServer.Data;

public static class DbInitializer
{
    private static readonly SemaphoreSlim InitLock = new(1, 1);

    public static async Task InitializeAsync(CeresDbContext db, CancellationToken ct = default)
    {
        await InitLock.WaitAsync(ct);
        try
        {
            // Per project guidance, avoid applying EF Core migrations automatically.
            // Use EnsureCreated to create the schema from the model for local/dev scenarios.
            await db.Database.EnsureCreatedAsync(ct);

            if (!await db.Tags.AnyAsync(ct))
            {
                var tags = new List<Tag>();
                for (int i = 0; i < 10; i++)
                {
                    var esn = (100000000000000 + i).ToString();
                    tags.Add(new Tag
                    {
                        Esn = esn,
                        Brand = "CeresTag",
                        FirmwareVersion = "1.2.3",
                        ActivationDate = DateTime.UtcNow.AddDays(-45),
                        BatteryPercentage = 90 + (i % 10),
                        Status = "Active",
                        PropertyId = "prop-001",
                        PropertyName = "Test Property",
                        LastSeen = DateTime.UtcNow.AddMinutes(-7)
                    });
                }
                await db.Tags.AddRangeAsync(tags, ct);
                await db.SaveChangesAsync(ct);
            }

            if (!await db.Observations.AnyAsync(ct))
            {
                var rnd = new Random(42);
                var start = DateTime.UtcNow.AddHours(-12);
                var observations = new List<Observation>();
                foreach (var tag in db.Tags)
                {
                    for (int i = 0; i < 300; i++)
                    {
                        var ts = start.AddMinutes(i * 2);
                        observations.Add(new Observation
                        {
                            Esn = tag.Esn,
                            Timestamp = ts,
                            Latitude = -27.0 + rnd.NextDouble() * 0.1,
                            Longitude = 152.0 + rnd.NextDouble() * 0.1,
                            Activity = i % 3 == 0 ? "grazing" : i % 3 == 1 ? "walking" : "resting",
                            Temperature = 25 + rnd.NextDouble() * 5,
                            Altitude = 300 + rnd.NextDouble() * 10,
                            Hdop = 0.7 + rnd.NextDouble() * 0.5
                        });
                    }
                }

                await db.Observations.AddRangeAsync(observations, ct);
                await db.SaveChangesAsync(ct);
            }

            // Optional customer/link seeding (only when enabled and tables are empty)
            // Enable by setting environment variable CERESMOCK_SEED_CUSTOMERS=true (or 1)
            var seedFlag = Environment.GetEnvironmentVariable("CERESMOCK_SEED_CUSTOMERS");
            var seedExtras = !string.IsNullOrWhiteSpace(seedFlag) &&
                             (seedFlag.Equals("true", StringComparison.OrdinalIgnoreCase) || seedFlag == "1");

            if (seedExtras)
            {
                if (!await db.Customers.AnyAsync(ct))
                {
                    var customers = new List<Customer>
                    {
                        new() { Id = "cust-abc", DisplayName = "Seamus O'Brien", CreatedAt = DateTime.UtcNow },
                        new() { Id = "cust-margaret-macleod", DisplayName = "Margaret MacLeod", CreatedAt = DateTime.UtcNow },
                        new() { Id = "cust-william-fitzgerald", DisplayName = "William Fitzgerald", CreatedAt = DateTime.UtcNow },
                        new() { Id = "cust-aoife-gallagher", DisplayName = "Aoife Gallagher", CreatedAt = DateTime.UtcNow },
                        new() { Id = "cust-eoin-mcdermott", DisplayName = "Eoin McDermott", CreatedAt = DateTime.UtcNow }
                    };
                    await db.Customers.AddRangeAsync(customers, ct);
                    await db.SaveChangesAsync(ct);
                }

                if (!await db.CustomerPropertyLinks.AnyAsync(ct))
                {
                    var links = new List<CustomerPropertyLink>
                    {
                        // For test compatibility: cust-abc linked to prop-1
                        new() { CustomerId = "cust-abc", PropertyId = "prop-1", PropertyName = "Blackwater Farm", LinkedAt = DateTime.UtcNow },

                        // Link some customers to existing and new rural properties
                        new() { CustomerId = "cust-abc", PropertyId = "prop-001", PropertyName = "Test Property", LinkedAt = DateTime.UtcNow },
                        new() { CustomerId = "cust-margaret-macleod", PropertyId = "prop-2", PropertyName = "Hillside Pastures", LinkedAt = DateTime.UtcNow },
                        new() { CustomerId = "cust-william-fitzgerald", PropertyId = "prop-3", PropertyName = "Glen View Ranch", LinkedAt = DateTime.UtcNow },
                        new() { CustomerId = "cust-aoife-gallagher", PropertyId = "prop-4", PropertyName = "Riverbend Paddocks", LinkedAt = DateTime.UtcNow }
                    };
                    await db.CustomerPropertyLinks.AddRangeAsync(links, ct);
                    await db.SaveChangesAsync(ct);
                }
            }
        }
        finally
        {
            InitLock.Release();
        }
    }
}


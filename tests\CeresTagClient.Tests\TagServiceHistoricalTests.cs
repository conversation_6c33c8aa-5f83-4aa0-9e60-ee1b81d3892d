using System.Net;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using CeresTagClient.Application.Services;
using CeresTagClient.Application.Validation;
using CeresTagClient.Core.Dtos;
using CeresTagClient.Core.Exceptions;
using CeresTagClient.Core.Interfaces;
using CeresTagClient.Core.Requests;
using FluentValidation;

namespace CeresTagClient.Tests;

public class TagServiceHistoricalTests
{
    private static ITagService CreateSut(Func<HttpRequestMessage, Task<HttpResponseMessage>> responder)
    {
        var httpHandler = new StubHttpHandler(responder);
        var pipeline = new StubAuthHandler("test-token") { InnerHandler = httpHandler };
        var httpClient = new HttpClient(pipeline) { BaseAddress = new Uri("https://mock") };
        var factory = new StubFactory(httpClient);

        IValidator<TagDetailsRequest> tagDetailsValidator = new TagDetailsRequestValidator();
        IValidator<RecentTagDataRequest> recentValidator = new RecentTagDataRequestValidator();
        IValidator<HistoricalRequest> historicalValidator = new HistoricalRequestValidator();
        IValidator<HistoricalRetrieveRequest> historicalRetrieveValidator = new HistoricalRetrieveRequestValidator();
        return new TagService(factory, tagDetailsValidator, recentValidator, historicalValidator, historicalRetrieveValidator);
    }

    [Fact]
    public async Task RequestHistorical_Success_Returns_RequestId()
    {
        var sut = CreateSut(async req =>
        {
            if (req.Method == HttpMethod.Post && req.RequestUri!.AbsolutePath.Contains("historical/request"))
            {
                var resp = new HistoricalRequestResponse { RequestId = "req-123" };
                return new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new StringContent(JsonSerializer.Serialize(resp), Encoding.UTF8, "application/json")
                };
            }
            return new HttpResponseMessage(HttpStatusCode.NotFound);
        });

        var result = await sut.RequestHistoricalDataAsync("100000000000000", DateTime.UtcNow.AddDays(-1), DateTime.UtcNow);
        Assert.Equal("req-123", result.RequestId);
    }

    [Fact]
    public async Task RequestHistorical_404_Maps_To_TagNotFoundException_With_ProblemDetails()
    {
        var sut = CreateSut(async req =>
        {
            var pd = new ProblemDetailsDto { Title = "tag_not_found", Status = 404, Detail = "Tag '999' not found", Type = "about:blank" };
            return new HttpResponseMessage(HttpStatusCode.NotFound)
            {
                Content = new StringContent(JsonSerializer.Serialize(pd), Encoding.UTF8, "application/problem+json")
            };
        });

        var ex = await Assert.ThrowsAsync<TagNotFoundException>(() => sut.RequestHistoricalDataAsync("999999999999999", DateTime.UtcNow.AddDays(-1), DateTime.UtcNow));
        Assert.Equal(404, ex.StatusCode);
        Assert.Equal("tag_not_found", ex.ProblemTitle);
        Assert.Equal("about:blank", ex.ProblemType);
        Assert.Contains("not found", ex.ProblemDetail!);
    }

    [Fact]
    public async Task RequestHistorical_400_Maps_To_ValidationFailedException_With_ProblemDetails()
    {
        var sut = CreateSut(async req =>
        {
            var pd = new ProblemDetailsDto { Title = "validation_failed", Status = 400, Detail = "endDate must be greater than startDate", Type = "about:blank" };
            return new HttpResponseMessage(HttpStatusCode.BadRequest)
            {
                Content = new StringContent(JsonSerializer.Serialize(pd), Encoding.UTF8, "application/problem+json")
            };
        });

        var ex = await Assert.ThrowsAsync<ValidationFailedException>(() => sut.RequestHistoricalDataAsync("100000000000000", DateTime.UtcNow.AddHours(-1), DateTime.UtcNow));
        Assert.Equal(400, ex.StatusCode);
        Assert.Equal("validation_failed", ex.ProblemTitle);
        Assert.Equal("about:blank", ex.ProblemType);
        Assert.Contains("greater", ex.ProblemDetail!);
    }

    [Fact]
    public async Task RequestHistorical_ClientSide_Validation_For_Range_Too_Large()
    {
        var sut = CreateSut(async _ => new HttpResponseMessage(HttpStatusCode.OK) { Content = new StringContent("{}") });
        var ex = await Assert.ThrowsAsync<ValidationFailedException>(() => sut.RequestHistoricalDataAsync("100000000000000", DateTime.UtcNow.AddDays(-40), DateTime.UtcNow));
        Assert.Equal(400, ex.StatusCode);
        Assert.Contains("31 days", ex.Message);
    }

    [Fact]
    public async Task GetHistorical_Success_Returns_Data_And_Pagination_For_PageSize_1()
    {
        var sut = CreateSut(async req =>
        {
            if (req.Method == HttpMethod.Get && req.RequestUri!.AbsolutePath.Contains("/historical/") && req.RequestUri!.Query.Contains("pageSize=1"))
            {
                var resp = new HistoricalRetrieveResponse
                {
                    Status = "ready",
                    Data = new List<RecentObservation> { new RecentObservation { Esn = "100000000000000", Timestamp = DateTime.UtcNow } },
                    Pagination = new PaginationMeta { TotalCount = 10, PageSize = 1, PageNumber = 1, HasNextPage = true }
                };
                return new HttpResponseMessage(HttpStatusCode.OK) { Content = new StringContent(JsonSerializer.Serialize(resp), Encoding.UTF8, "application/json") };
            }
            return new HttpResponseMessage(HttpStatusCode.NotFound);
        });

        var result = await sut.GetHistoricalDataAsync("100000000000000", "req-123", pageSize: 1, pageNumber: 1);
        Assert.Equal("ready", result.Status);
        Assert.Equal(1, result.Pagination.PageSize);
        Assert.True(result.Pagination.HasNextPage);
        Assert.Single(result.Data);
    }

    [Fact]
    public async Task GetHistorical_Success_Boundary_PageSize_1000()
    {
        var sut = CreateSut(async req =>
        {
            if (req.Method == HttpMethod.Get && req.RequestUri!.AbsolutePath.Contains("/historical/") && req.RequestUri!.Query.Contains("pageSize=1000"))
            {
                var resp = new HistoricalRetrieveResponse
                {
                    Status = "ready",
                    Data = new List<RecentObservation>(),
                    Pagination = new PaginationMeta { TotalCount = 1000, PageSize = 1000, PageNumber = 1, HasNextPage = false }
                };
                return new HttpResponseMessage(HttpStatusCode.OK) { Content = new StringContent(JsonSerializer.Serialize(resp), Encoding.UTF8, "application/json") };
            }
            return new HttpResponseMessage(HttpStatusCode.NotFound);
        });

        var result = await sut.GetHistoricalDataAsync("100000000000000", "req-123", pageSize: 1000, pageNumber: 1);
        Assert.Equal(1000, result.Pagination.PageSize);
        Assert.False(result.Pagination.HasNextPage);
    }

    [Fact]
    public async Task GetHistorical_404_Maps_To_TagNotFoundException_With_ProblemDetails()
    {
        var sut = CreateSut(async req =>
        {
            var pd = new ProblemDetailsDto { Title = "tag_not_found", Status = 404, Detail = "Tag missing", Type = "about:blank" };
            return new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent(JsonSerializer.Serialize(pd), Encoding.UTF8, "application/problem+json") };
        });

        var ex = await Assert.ThrowsAsync<TagNotFoundException>(() => sut.GetHistoricalDataAsync("999999999999999", "req-123"));
        Assert.Equal("tag_not_found", ex.ProblemTitle);
        Assert.Equal("about:blank", ex.ProblemType);
        Assert.Contains("missing", ex.ProblemDetail!);
    }

    [Fact]
    public async Task GetHistorical_400_Invalid_Pagination_Maps_To_ValidationException()
    {
        var sut = CreateSut(async req =>
        {
            var pd = new ProblemDetailsDto { Title = "validation_failed", Status = 400, Detail = "Invalid pagination", Type = "about:blank" };
            return new HttpResponseMessage(HttpStatusCode.BadRequest) { Content = new StringContent(JsonSerializer.Serialize(pd), Encoding.UTF8, "application/problem+json") };
        });

        var ex = await Assert.ThrowsAsync<ValidationFailedException>(() => sut.GetHistoricalDataAsync("100000000000000", "req-123", pageSize: 0, pageNumber: 0));
        Assert.Equal("validation_failed", ex.ProblemTitle);
        Assert.Equal("about:blank", ex.ProblemType);
        Assert.Contains("pagination", ex.ProblemDetail!);
    }

    private class StubAuthHandler(string token) : DelegatingHandler
    {
        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            return base.SendAsync(request, cancellationToken);
        }
    }

    private class StubHttpHandler(Func<HttpRequestMessage, Task<HttpResponseMessage>> responder) : HttpMessageHandler
    {
        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
            => responder(request);
    }

    private class StubFactory(HttpClient client) : IHttpClientFactory
    {
        public HttpClient CreateClient(string name) => client;
    }
}


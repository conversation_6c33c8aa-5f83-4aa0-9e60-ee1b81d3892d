using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;

namespace CeresMockServer.Tests;

using CeresMockServer;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;

public class WebhooksControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public WebhooksControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["ConnectionStrings:CeresDb"] = $"Data Source=webhooks-tests-{Guid.NewGuid():N}.db"
                };
                config.AddInMemoryCollection(dict);
            });
        });
    }

    [Fact]
    public async Task Create_List_Get_Update_Delete_Webhook_Works()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var create = new WebhookCreateRequest
        {
            Url = "https://example.com/hook",
            Type = "alert",
            SharedSecret = "supersecret123",
            Metadata = "test",
            Active = true
        };

        // Create
        var createResp = await client.PostAsJsonAsync("/api/v1/webhooks", create);
        Assert.Equal(HttpStatusCode.Created, createResp.StatusCode);
        var created = await createResp.Content.ReadFromJsonAsync<WebhookResponse>();
        Assert.NotNull(created);
        Assert.Equal(create.Url, created!.Url);
        Assert.Equal(create.Type, created.Type);

        // List
        var listResp = await client.GetAsync("/api/v1/webhooks?type=alert");
        Assert.Equal(HttpStatusCode.OK, listResp.StatusCode);
        var list = await listResp.Content.ReadFromJsonAsync<List<WebhookResponse>>();
        Assert.NotNull(list);
        Assert.Contains(list!, x => x.Id == created.Id);

        // Get by id
        var getResp = await client.GetAsync($"/api/v1/webhooks/{created.Id}");
        Assert.Equal(HttpStatusCode.OK, getResp.StatusCode);
        var got = await getResp.Content.ReadFromJsonAsync<WebhookResponse>();
        Assert.NotNull(got);
        Assert.Equal(created.Id, got!.Id);

        // Update
        var update = new WebhookUpdateRequest { Metadata = "changed", Active = false };
        var putResp = await client.PutAsJsonAsync($"/api/v1/webhooks/{created.Id}", update);
        Assert.Equal(HttpStatusCode.NoContent, putResp.StatusCode);

        // Confirm update
        var getAfter = await client.GetAsync($"/api/v1/webhooks/{created.Id}");
        var gotAfter = await getAfter.Content.ReadFromJsonAsync<WebhookResponse>();
        Assert.Equal("changed", gotAfter!.Metadata);
        Assert.False(gotAfter.Active);

        // Delete
        var delResp = await client.DeleteAsync($"/api/v1/webhooks/{created.Id}");
        Assert.Equal(HttpStatusCode.NoContent, delResp.StatusCode);

        // Get should 404
        var notFound = await client.GetAsync($"/api/v1/webhooks/{created.Id}");
        Assert.Equal(HttpStatusCode.NotFound, notFound.StatusCode);
        Assert.Equal("application/problem+json; charset=utf-8", notFound.Content.Headers.ContentType!.ToString());
        var pd = await notFound.Content.ReadFromJsonAsync<ProblemDetails>();
        Assert.Equal("webhook_not_found", pd!.Title);
        Assert.Equal("about:blank", pd.Type);
    }

    [Fact]
    public async Task Create_Duplicate_Url_Type_Fails()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var create = new WebhookCreateRequest
        {
            Url = "https://example.com/hook",
            Type = "alert",
            SharedSecret = "supersecret123",
        };

        var createResp1 = await client.PostAsJsonAsync("/api/v1/webhooks", create);
        Assert.Equal(HttpStatusCode.Created, createResp1.StatusCode);

        var createResp2 = await client.PostAsJsonAsync("/api/v1/webhooks", create);
        Assert.Equal(HttpStatusCode.BadRequest, createResp2.StatusCode);
        Assert.Equal("application/problem+json; charset=utf-8", createResp2.Content.Headers.ContentType!.ToString());
        var pd = await createResp2.Content.ReadFromJsonAsync<ProblemDetails>();
        Assert.Equal("validation_failed", pd!.Title);
        Assert.Equal("about:blank", pd.Type);
    }
}


using System.Text.Json.Serialization;

namespace CeresMockServer.Models;

public class HistoricalRequestDto
{
    [JsonPropertyName("startDate")] public DateTime StartDate { get; set; }
    [JsonPropertyName("endDate")] public DateTime EndDate { get; set; }
}

public class HistoricalRequestResponse
{
    [JsonPropertyName("requestId")] public string RequestId { get; set; } = string.Empty;
}

public class HistoricalRetrieveResponse
{
    [JsonPropertyName("status")] public string Status { get; set; } = "ready";
    [JsonPropertyName("data")] public List<RecentObservation> Data { get; set; } = new();
    [JsonPropertyName("pagination")] public PaginationMeta Pagination { get; set; } = new();
}


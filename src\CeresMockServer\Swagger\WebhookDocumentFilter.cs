using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace CeresMockServer.Swagger;

public class WebhookDocumentFilter : IDocumentFilter
{
    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        swaggerDoc.Components ??= new OpenApiComponents();
        swaggerDoc.Components.Schemas ??= new Dictionary<string, OpenApiSchema>();
        swaggerDoc.Components.Examples ??= new Dictionary<string, OpenApiExample>();

        // Alert payload schema (already defined in AlertDocumentFilter); keep here for completeness if needed
        if (!swaggerDoc.Components.Schemas.ContainsKey("AlertWebhookPayload"))
        {
            swaggerDoc.Components.Schemas["AlertWebhookPayload"] = new OpenApiSchema
            {
                Type = "object",
                Properties = new Dictionary<string, OpenApiSchema>
                {
                    ["type"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("alert") },
                    ["version"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("1.0") },
                    ["alert_type"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("high_activity") },
                    ["esn"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("100000000000000") },
                    ["timestamp"] = new OpenApiSchema { Type = "string", Format = "date-time" },
                    ["severity"] = new OpenApiSchema { Type = "string" },
                    ["location"] = new OpenApiSchema
                    {
                        Type = "object",
                        Nullable = true,
                        Properties = new Dictionary<string, OpenApiSchema>
                        {
                            ["lat"] = new OpenApiSchema { Type = "number", Format = "double" },
                            ["lon"] = new OpenApiSchema { Type = "number", Format = "double" }
                        }
                    },
                    ["battery_level"] = new OpenApiSchema { Type = "integer", Format = "int32", Nullable = true },
                    ["activity_summary"] = new OpenApiSchema
                    {
                        Type = "object",
                        Nullable = true,
                        Properties = new Dictionary<string, OpenApiSchema>
                        {
                            ["window_minutes"] = new OpenApiSchema { Type = "integer", Format = "int32" },
                            ["ratio"] = new OpenApiSchema { Type = "number", Format = "double" }
                        }
                    }
                },
                Required = new HashSet<string> { "type", "version", "alert_type", "esn", "timestamp", "severity" }
            };
        }

        // PFI payload schema
        swaggerDoc.Components.Schemas["PfiWebhookPayload"] = new OpenApiSchema
        {
            Type = "object",
            Properties = new Dictionary<string, OpenApiSchema>
            {
                ["type"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("pfi") },
                ["version"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("1.0") },
                ["date"] = new OpenApiSchema { Type = "string", Format = "date" },
                ["esn"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("100000000000000") },
                ["pfi"] = new OpenApiSchema
                {
                    Type = "object",
                    Properties = new Dictionary<string, OpenApiSchema>
                    {
                        ["score"] = new OpenApiSchema { Type = "number", Format = "double" },
                        ["herd"] = new OpenApiSchema { Type = "number", Format = "double" },
                        ["individual"] = new OpenApiSchema { Type = "number", Format = "double" }
                    }
                }
            },
            Required = new HashSet<string> { "type", "version", "date", "esn", "pfi" }
        };

        // Historical payload schema
        swaggerDoc.Components.Schemas["HistoricalWebhookPayload"] = new OpenApiSchema
        {
            Type = "object",
            Properties = new Dictionary<string, OpenApiSchema>
            {
                ["type"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("historical") },
                ["version"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("1.0") },
                ["request_id"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("req_abcdef") },
                ["status"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("ready") }
            },
            Required = new HashSet<string> { "type", "version", "request_id", "status" }
        };

        // Standard payload schema
        swaggerDoc.Components.Schemas["StandardWebhookPayload"] = new OpenApiSchema
        {
            Type = "object",
            Properties = new Dictionary<string, OpenApiSchema>
            {
                ["type"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("standard") },
                ["version"] = new OpenApiSchema { Type = "string", Example = new OpenApiString("1.0") },
                ["ts"] = new OpenApiSchema { Type = "string", Format = "date-time" },
                ["data"] = new OpenApiSchema
                {
                    Type = "array",
                    Items = new OpenApiSchema
                    {
                        Type = "object",
                        Properties = new Dictionary<string, OpenApiSchema>
                        {
                            ["esn"] = new OpenApiSchema { Type = "string" },
                            ["lat"] = new OpenApiSchema { Type = "number", Format = "double" },
                            ["lon"] = new OpenApiSchema { Type = "number", Format = "double" }
                        }
                    }
                }
            },
            Required = new HashSet<string> { "type", "version", "ts", "data" }
        };

        // Examples
        swaggerDoc.Components.Examples["PfiWebhookExample"] = new OpenApiExample { Value = OpenApiAnyFactory.CreateFromJson(Examples.PfiWebhookExample.Sample) };
        swaggerDoc.Components.Examples["HistoricalWebhookExample"] = new OpenApiExample { Value = OpenApiAnyFactory.CreateFromJson(Examples.HistoricalWebhookExample.Sample) };
        swaggerDoc.Components.Examples["StandardWebhookExample"] = new OpenApiExample { Value = OpenApiAnyFactory.CreateFromJson(Examples.StandardWebhookExample.Sample) };
    }
}


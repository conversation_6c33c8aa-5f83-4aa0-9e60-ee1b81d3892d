using CeresMockServer.Data;
using CeresMockServer.Options;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace CeresMockServer.Services;

public class WebhookScheduler : BackgroundService
{
    private readonly IWebhookQueue _queue;
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly IWebhookPayloadFactory _payloads;
    private readonly WebhookScheduleOptions _opts;
    private readonly ILogger<WebhookScheduler> _logger;

    public WebhookScheduler(IWebhookQueue queue, IServiceScopeFactory scopeFactory, IWebhookPayloadFactory payloads, IOptions<WebhookScheduleOptions> opts, ILogger<WebhookScheduler> logger)
    {
        _queue = queue;
        _scopeFactory = scopeFactory;
        _payloads = payloads;
        _opts = opts.Value;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!_opts.EnableScheduler) return;

        var tasks = new List<Task>();
        if (_opts.AlertIntervalMs > 0) tasks.Add(RunTypeLoop("alert", _opts.AlertIntervalMs, () => _payloads.CreateAlert(), stoppingToken));
        if (_opts.PfiIntervalMs > 0) tasks.Add(RunTypeLoop("pfi", _opts.PfiIntervalMs, () => _payloads.CreatePfi(), stoppingToken));
        if (_opts.HistoricalIntervalMs > 0) tasks.Add(RunTypeLoop("historical", _opts.HistoricalIntervalMs, () => _payloads.CreateHistorical(), stoppingToken));
        if (_opts.StandardIntervalMs > 0) tasks.Add(RunTypeLoop("standard", _opts.StandardIntervalMs, () => _payloads.CreateStandard(), stoppingToken));

        await Task.WhenAll(tasks);
    }

    private async Task RunTypeLoop(string type, int intervalMs, Func<string> payloadFn, CancellationToken ct)
    {
        while (!ct.IsCancellationRequested)
        {
            try
            {
                using var scope = _scopeFactory.CreateScope();
                var db = scope.ServiceProvider.GetRequiredService<CeresDbContext>();
                var regs = await db.Webhooks.Where(w => w.Active && w.Type == type).ToListAsync(ct);
                var payload = payloadFn();
                foreach (var r in regs)
                    _queue.Enqueue(new WebhookMessage(r.Id, r.Url, r.SharedSecret, r.Type, payload, DateTime.UtcNow));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Scheduler loop error for {Type}", type);
            }

            await Task.Delay(intervalMs, ct);
        }
    }
}


using System.Text.Json.Serialization;

namespace CeresMockServer.Models;

public class TagDetailsResponse
{
    [JsonPropertyName("esn")] public string Esn { get; set; } = string.Empty;
    [JsonPropertyName("brand")] public string Brand { get; set; } = "CeresTag";
    [JsonPropertyName("firmwareVersion")] public string FirmwareVersion { get; set; } = "1.0.0";
    [JsonPropertyName("activationDate")] public DateTime ActivationDate { get; set; } = DateTime.UtcNow.AddDays(-30);
    [JsonPropertyName("batteryPercentage")] public int BatteryPercentage { get; set; } = 95;
    [JsonPropertyName("status")] public string Status { get; set; } = "Active";
    [JsonPropertyName("property")] public PropertyDto Property { get; set; } = new();
    [JsonPropertyName("lastSeen")] public DateTime? LastSeen { get; set; } = DateTime.UtcNow.AddMinutes(-5);
}

public class PropertyDto
{
    [JsonPropertyName("id")] public string Id { get; set; } = "prop-001";
    [JsonPropertyName("name")] public string Name { get; set; } = "Test Property";
}

public class RecentTagDataResponse
{
    [JsonPropertyName("data")] public List<RecentObservation> Data { get; set; } = new();
    [JsonPropertyName("pagination")] public PaginationMeta Pagination { get; set; } = new();
}

public class RecentObservation
{
    [JsonPropertyName("esn")] public string Esn { get; set; } = string.Empty;
    [JsonPropertyName("timestamp")] public DateTime Timestamp { get; set; }
    [JsonPropertyName("latitude")] public double Latitude { get; set; }
    [JsonPropertyName("longitude")] public double Longitude { get; set; }
    [JsonPropertyName("activity")] public string Activity { get; set; } = "grazing";
    [JsonPropertyName("temperature")] public double Temperature { get; set; }
    [JsonPropertyName("altitude")] public double? Altitude { get; set; }
    [JsonPropertyName("hdop")] public double? Hdop { get; set; }
}

public class PaginationMeta
{
    [JsonPropertyName("totalCount")] public int TotalCount { get; set; }
    [JsonPropertyName("pageSize")] public int PageSize { get; set; }
    [JsonPropertyName("pageNumber")] public int PageNumber { get; set; }
    [JsonPropertyName("hasNextPage")] public bool HasNextPage { get; set; }
}


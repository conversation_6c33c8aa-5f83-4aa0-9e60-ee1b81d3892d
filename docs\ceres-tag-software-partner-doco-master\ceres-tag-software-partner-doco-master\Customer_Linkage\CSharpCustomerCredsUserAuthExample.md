## Example C# Software Partner Customer Authentication



## Overview

C # example Code on how to configure Software Partner Customer Authentication

## Sample Code

```C#
// Author: <PERSON> (SatanEnglish)
// Date: 17th March 2021
// Description: C # example Code

/// <summary>
/// The location to redirect to on successful customer login.
/// This is a standard Oauth 2 callback. Containing the access_token, state ..etc
/// </summary>
/// <returns></returns>
[HttpGet]
[Route("SigninCallback")]
public ActionResult SigninCallback()
{
  var resourceName = "ceresportal_Software_API.htmlpage.html";
  var assmebly = GetType().Assembly;
  var stream = assmebly.GetManifestResourceStream(resourceName);
  stream.Seek(0, SeekOrigin.Begin);

  string result = null;            
  using (var sRead = new StreamReader(stream))
  {
    result = sRead.ReadToEnd();
  }

  return new ContentResult
  {
    ContentType = "text/html",
    Content = result,
  };
}

/// <summary>
/// The location to redirect to on successful login.
/// This is a standard Oauth 2 callback
/// NOTE: You MUST use GET within the Ceres Portal environment. This is an example only.
/// </summary>
/// <returns></returns>
[HttpGet]
[Route("SigninCallback")]
public async Task<ActionResult> SigninCallback([FromForm] string access_token, [FromForm]  string id_token,
                                [FromForm] string state, [FromForm]  string token_type, [FromForm]  int expires_in)
{
  SoftwareAccountCallback callback = null;
  string result = null;
  try
  {
    var client = new HttpClient();
    client.DefaultRequestHeaders.Add("Authorization", $"Bearer {access_token}");
    callback = _softwareVerificationService.GetSoftwareAccountCallback(new Guid(state));
    var endpoint = callback.SoftwareVendor.ClientIdentitiesEndPoint;
    endpoint = endpoint.Replace("http://localhost", "http://host.docker.internal");
    var res = await client.GetAsync(endpoint); 
    if (res.IsSuccessStatusCode)
    {
      result = await res.Content.ReadAsStringAsync();
    }
    _softwareVerificationService.UpdateSoftwareAccountCallback(new Guid(state), result);
  }
  catch (Exception ex)
  {
    _logger.Error("User Authentication SigninCallback has had an issue");
    if(callback != null)
    {
      _logger.Error("Callback database object" + Environment.NewLine + JsonConvert.SerializeObject(callback, Formatting.Indented));
    }
    if(result != null)
    {
      _logger.Error("User Authentication SigninCallback result" + Environment.NewLine + JsonConvert.SerializeObject(result, Formatting.Indented));
    }

    _logger.Error(ex, "User Authentication SigninCallback exception");
    throw ex;
  }

  var resourceName = "ceresportal_Software_API.callbackFinished.html";
  var assmebly = GetType().Assembly;
  var stream = assmebly.GetManifestResourceStream(resourceName);
  stream.Seek(0, SeekOrigin.Begin);

  string finishedPage = null;
  // use streamreader
  using (var sRead = new StreamReader(stream))
  {
    // read to string
    finishedPage = sRead.ReadToEnd();
  }

  return new ContentResult
  {
    ContentType = "text/html",
    Content = finishedPage,
  };
}

```

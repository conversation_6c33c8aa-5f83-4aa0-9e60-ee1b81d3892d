import { TagDetailsSchema, RecentDataResponseSchema, TagDetails, RecentDataResponse } from './types';

const CERES_PROXY_BASE = '/api/ceres';

export async function fetchTagDetails(esn: string): Promise<TagDetails> {
  const resp = await fetch(`${CERES_PROXY_BASE}/api/v1/tags/${esn}`, { cache: 'no-store' });
  if (!resp.ok) throw new Error(`Failed to fetch tag details: ${resp.status}`);
  const data = await resp.json();
  return TagDetailsSchema.parse(data);
}

export async function fetchRecent(esn: string, fromDate?: string, pageSize = 200) : Promise<RecentDataResponse> {
  const q = new URLSearchParams();
  if (fromDate) q.set('fromDate', fromDate);
  q.set('pageSize', String(pageSize));
  const resp = await fetch(`${CERES_PROXY_BASE}/api/v1/tags/${esn}/recent?${q.toString()}`, { cache: 'no-store' });
  if (!resp.ok) throw new Error(`Failed to fetch recent data: ${resp.status}`);
  const data = await resp.json();
  return RecentDataResponseSchema.parse(data);
}


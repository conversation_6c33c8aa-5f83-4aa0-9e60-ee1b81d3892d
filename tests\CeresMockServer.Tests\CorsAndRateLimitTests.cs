using System.Net;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;

namespace CeresMockServer.Tests;

public class CorsAndRateLimitTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public CorsAndRateLimitTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Replace the default limiter with a tighter one for deterministic testing
                var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(CeresMockServer.RateLimiting.SimpleRateLimiter));
                if (descriptor != null)
                    services.Remove(descriptor);
                services.AddSingleton(new CeresMockServer.RateLimiting.SimpleRateLimiter(2));
            });
        });
    }

    [Fact]
    public async Task RateLimit_Returns_429_After_Limit()
    {
        var client = _factory.CreateClient();
        var ok1 = await client.GetAsync("/health/live");
        var ok2 = await client.GetAsync("/health/live");
        var limited = await client.GetAsync("/health/live");
        Assert.Equal(HttpStatusCode.OK, ok1.StatusCode);
        Assert.Equal(HttpStatusCode.OK, ok2.StatusCode);
        Assert.Equal(HttpStatusCode.TooManyRequests, limited.StatusCode);
    }

    [Fact]
    public async Task Cors_Preflight_Returns_204_And_Header_Present()
    {
        var client = _factory.CreateClient(new WebApplicationFactoryClientOptions { HandleCookies = false });
        using var req = new HttpRequestMessage(HttpMethod.Options, "/health/live");
        req.Headers.Add("Origin", "https://example.com");
        req.Headers.Add("Access-Control-Request-Method", "GET");
        var resp = await client.SendAsync(req);
        Assert.Equal(HttpStatusCode.NoContent, resp.StatusCode);
        // Default policy allows any origin when none configured; header may be "*" or an echoed origin
        Assert.True(resp.Headers.TryGetValues("Access-Control-Allow-Origin", out var values));
        var val = values!.Single();
        Assert.True(val == "*" || val == "https://example.com");
    }
}


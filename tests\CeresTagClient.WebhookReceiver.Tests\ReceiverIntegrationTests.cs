using System.Net.Http.Headers;
using System.Net.Http.Json;
using CeresMockServer.Models;
using CeresTagClient.WebhookReceiver.Services;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using CeresMockServer;
using CeresMockServer.Services;

namespace CeresTagClient.WebhookReceiver.Tests;

public class ReceiverIntegrationTests : IClassFixture<WebApplicationFactory<Program>>, IAsyncLifetime, IDisposable
{
    private readonly WebApplicationFactory<Program> _mockFactory;
    private readonly WebApplicationFactory<CeresTagClient.WebhookReceiver.Program> _receiverFactory;
    private HttpClient? _mockClient;
    private HttpClient? _receiverClient;

    public ReceiverIntegrationTests(WebApplicationFactory<Program> mockFactory)
    {
        _mockFactory = mockFactory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["CeresMock:Authentication:Issuer"] = "https://mock.cerestag.com",
                    ["CeresMock:Authentication:Audience"] = "ceres-api",
                    ["CeresMock:Authentication:SigningKey"] = "dev-super-secret-signing-key-change-me",
                    ["CeresMock:Authentication:TokenExpirySeconds"] = "7200",
                    ["ConnectionStrings:CeresDb"] = $"Data Source=receiver-int-{Guid.NewGuid():N}.db"
                };
                config.AddInMemoryCollection(dict);
            });
        });
        _receiverFactory = new WebApplicationFactory<CeresTagClient.WebhookReceiver.Program>().WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["WebhookReceiver:SharedSecret"] = "secretsecret",
                    ["CeresApi:BaseUrl"] = "http://localhost/"
                };
                config.AddInMemoryCollection(dict);
            });
        });

        // Synchronous initialization only; async setup moved to InitializeAsync
        _mockClient = _mockFactory.CreateClient();
        _receiverClient = _receiverFactory.CreateClient(new WebApplicationFactoryClientOptions { AllowAutoRedirect = false });
    }


    public async Task InitializeAsync()
    {
        // Acquire OAuth token via DI service (avoids routing issues in tests) and set Authorization header
        var auth = _mockFactory.Services.GetRequiredService<CeresMockServer.Services.IAuthenticationService>();
        var tuple = await auth.TryGetTokenAsync("test-client-1", "test-secret-1", scope: "read write");
        Assert.True(tuple.Success);
        _mockClient!.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tuple.AccessToken);
    }

    public Task DisposeAsync()
    {
        // No async disposal currently required
        return Task.CompletedTask;
    }

    [Theory]
    [InlineData("alert", "/api/v1/webhooks/simulate/alert")]
    [InlineData("pfi", "/api/v1/webhooks/simulate/pfi")]
    [InlineData("historical", "/api/v1/webhooks/simulate/historical")]
    [InlineData("standard", "/api/v1/webhooks/simulate/standard")]
    public async Task Receiver_Accepts_Signed_Webhooks_From_Mock(string type, string simulatePath)
    {
        var receiverUrl = _receiverClient.BaseAddress!.ToString().TrimEnd('/') + "/api/webhooks/receive";
        var sharedSecret = "secretsecret";

        // Register webhook in mock server pointing to its local test sink (so we can inspect the exact signed request)
        var uniquePath = $"/api/v1/test-webhook/{type}-{Guid.NewGuid():N}";
        var hook = new CeresMockServer.Models.WebhookCreateRequest
        {
            Url = uniquePath,
            Type = type,
            SharedSecret = sharedSecret,
            Active = true
        };
        var createResp = await _mockClient.PostAsJsonAsync("/api/v1/webhooks", hook);
        createResp.EnsureSuccessStatusCode();

        // clear any previous receipts for this path
        await _mockClient.DeleteAsync($"/api/v1/test-webhook?path={Uri.EscapeDataString(uniquePath)}");

        // Trigger simulator to produce a signed webhook into the mock server's test sink
        var simResp = await _mockClient.PostAsync(simulatePath, null);
        Assert.Equal(System.Net.HttpStatusCode.Accepted, simResp.StatusCode);

        // Fetch captured signed request (headers + body)
        var inspect = await _mockClient.GetAsync($"/api/v1/test-webhook/inspect?path={Uri.EscapeDataString(uniquePath)}");
        var items = await inspect.Content.ReadFromJsonAsync<List<dynamic>>();
        Assert.NotNull(items);
        Assert.True(items!.Count > 0);

        var first = (System.Text.Json.JsonElement)items![0];
        var headersElem = first.TryGetProperty("headers", out var h1) ? h1 : first.GetProperty("Headers");
        string? signature = null;
        foreach (var prop in headersElem.EnumerateObject())
        {
            if (string.Equals(prop.Name, "X-Ceres-Signature", StringComparison.OrdinalIgnoreCase))
            {
                signature = prop.Value.GetString();
                break;
            }
        }
        Assert.False(string.IsNullOrEmpty(signature));

        var bodyElem = first.TryGetProperty("body", out var b1) ? b1 : first.GetProperty("Body");
        var body = bodyElem.GetString() ?? string.Empty;
        Assert.False(string.IsNullOrEmpty(body));

        // Now forward the same signed payload to our receiver
        var req = new HttpRequestMessage(HttpMethod.Post, receiverUrl)
        {
            Content = new StringContent(body, System.Text.Encoding.UTF8, "application/json")
        };
        req.Headers.Add("X-Ceres-Signature", signature);
        var recvResp = await _receiverClient.SendAsync(req);
        Assert.Equal(System.Net.HttpStatusCode.OK, recvResp.StatusCode);

        // Poll receiver queue via DI to ensure message was enqueued
        var queue = _receiverFactory.Services.GetRequiredService<IMessageQueue>();
        for (int i = 0; i < 50; i++)
        {
            if (queue.TryDequeue(out var msg) && msg is not null)
            {
                Assert.False(string.IsNullOrWhiteSpace(msg.Payload));
                return;
            }
            await Task.Delay(100);
        }
        Assert.Fail("No message received by receiver within timeout");
    }

    public void Dispose()
    {
        _mockClient?.Dispose();
        _receiverClient?.Dispose();
        _mockFactory?.Dispose();
        _receiverFactory?.Dispose();
    }
}


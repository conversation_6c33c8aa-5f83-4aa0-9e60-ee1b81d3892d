using System.Text.Json;
using CeresMockServer.Data;
using CeresMockServer.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CeresMockServer.Controllers;

[ApiController]
[Authorize]
[Route("api/v1/webhooks/simulate")]
public class WebhookSimulateController(CeresDbContext db, IWebhookQueue queue, IWebhookPayloadFactory payloads) : ControllerBase
{
    /// <summary>
    /// Simulates an Alert webhook delivery to all registered Alert webhooks.
    /// </summary>
    /// <response code="202">Webhook queued for delivery.</response>
    [HttpPost("alert")]
    [ProducesResponseType(StatusCodes.Status202Accepted)]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Alert(CancellationToken ct)
    {
        var payload = payloads.CreateAlert();
        await EnqueueForType("alert", payload, ct);
        return Accepted();
    }

    /// <summary>
    /// Simulates a PFI webhook delivery to all registered PFI webhooks.
    /// </summary>
    /// <response code="202">Webhook queued for delivery.</response>
    [HttpPost("pfi")]
    [ProducesResponseType(StatusCodes.Status202Accepted)]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Pfi(CancellationToken ct)
    {
        var payload = payloads.CreatePfi();
        await EnqueueForType("pfi", payload, ct);
        return Accepted();
    }

    /// <summary>
    /// Simulates a Historical webhook delivery (request ready notification) to all registered Historical webhooks.
    /// </summary>
    /// <response code="202">Webhook queued for delivery.</response>
    [HttpPost("historical")]
    [ProducesResponseType(StatusCodes.Status202Accepted)]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Historical(CancellationToken ct)
    {
        var payload = payloads.CreateHistorical();
        await EnqueueForType("historical", payload, ct);
        return Accepted();
    }

    /// <summary>
    /// Simulates a Standard data webhook delivery to all registered Standard webhooks.
    /// </summary>
    /// <response code="202">Webhook queued for delivery.</response>
    [HttpPost("standard")]
    [ProducesResponseType(StatusCodes.Status202Accepted)]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Standard(CancellationToken ct)
    {
        var payload = payloads.CreateStandard();
        await EnqueueForType("standard", payload, ct);
        return Accepted();
    }

    private async Task EnqueueForType(string type, string payload, CancellationToken ct)
    {
        var regs = await db.Webhooks.Where(w => w.Active && w.Type == type).ToListAsync(ct);
        foreach (var r in regs)
        {
            queue.Enqueue(new WebhookMessage(r.Id, r.Url, r.SharedSecret, r.Type, payload, DateTime.UtcNow));
        }
    }
}


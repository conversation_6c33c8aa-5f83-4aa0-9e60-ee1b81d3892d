using System.Net;
using System.Text.Json;
using CeresTagClient.Application.Services;
using CeresTagClient.Application.Validation;
using CeresTagClient.Core.Dtos;
using CeresTagClient.Core.Interfaces;
using CeresTagClient.Core.Requests;
using FluentValidation;

namespace CeresTagClient.Tests;

public class TagServiceTests
{
    [Fact]
    public async Task FirstContact_Includes_Authorization_Header()
    {
        var token = "abc123";

        var httpHandler = new StubHttpHandler(async req =>
        {
            Assert.True(req.Headers.Authorization?.Scheme == "Bearer");
            Assert.True(req.Headers.Authorization?.Parameter == token);
            var resp = new FirstContactResponse { Status = "ok", Server = "CeresMockServer", Time = DateTime.UtcNow };
            return new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(JsonSerializer.Serialize(resp))
            };
        });

        var pipeline = new StubAuthHandler(token) { InnerHandler = httpHandler };
        var httpClient = new HttpClient(pipeline) { BaseAddress = new Uri("https://mock") };
        var factory = new StubFactory(httpClient);

        IValidator<TagDetailsRequest> tagDetailsValidator = new TagDetailsRequestValidator();
        IValidator<RecentTagDataRequest> recentValidator = new RecentTagDataRequestValidator();
        IValidator<HistoricalRequest> historicalValidator = new HistoricalRequestValidator();
        IValidator<HistoricalRetrieveRequest> historicalRetrieveValidator = new HistoricalRetrieveRequestValidator();
        ITagService sut = new TagService(factory, tagDetailsValidator, recentValidator, historicalValidator, historicalRetrieveValidator);

        var result = await sut.FirstContactAsync();

        Assert.Equal("ok", result.Status);
        Assert.Equal("CeresMockServer", result.Server);
    }

    private class StubAuthHandler(string token) : DelegatingHandler
    {
        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            return base.SendAsync(request, cancellationToken);
        }
    }

    private class StubHttpHandler(Func<HttpRequestMessage, Task<HttpResponseMessage>> responder) : HttpMessageHandler
    {
        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
            => responder(request);
    }

    private class StubFactory(HttpClient client) : IHttpClientFactory
    {
        public HttpClient CreateClient(string name) => client;
    }
}


using System.Collections.Concurrent;

namespace CeresMockServer.RateLimiting;

public class SimpleRateLimiter
{
    private readonly int _limitPerMinute;
    private readonly ConcurrentDictionary<string, (int Count, DateTime WindowStartUtc)> _buckets = new();

    public SimpleRateLimiter(int limitPerMinute)
    {
        _limitPerMinute = Math.Max(1, limitPerMinute);
    }

    public bool Allow(string key)
    {
        var now = DateTime.UtcNow;
        var windowStart = new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute, 0, DateTimeKind.Utc);
        var entry = _buckets.AddOrUpdate(key, _ => (1, windowStart), (_, existing) =>
        {
            if (existing.WindowStartUtc != windowStart) return (1, windowStart);
            var next = existing.Count + 1;
            return (next, windowStart);
        });
        return entry.Count <= _limitPerMinute;
    }
}


# API Data Discovery

As a part of integration,you will be given a CERES PORTAL software account in our **TEST Environment** so you can access and ingest CERES TAG synthetic data by querying our API. If you already have a **TEST** account, you can login [here](https://test.cerestag.com/home).

Once you are an <u>Approved Software Partner</u> (to get this you must pass our [minimum requirements checklist](../Minimum_Requirements.pdf)), your will be provided with a production account.

The synthetic dataset allows you to access and understand our data structure and begin the development work required to ingest and visualise the data prior to becoming an <u>Approved Software Partner</u> and receiving live data from customers who have purchased devices.

The CERES PORTAL uses the **OAuth2 Client Credential flow** to allow your application to authenticate before requesting device data. For more information about this type of flow please check out this [EXAMPLE](https://auth0.com/docs/get-started/authentication-and-authorization-flow/client-credentials-flow)

You can generate your **OAuth2 credentials (API Client_ID & Secret)** through your [CERES PORTAL software account dashboard ](https://test.cerestag.com/dashboard)

These credentials can be used to authenticate **M2M** (Machine to Machine) and it is the responsibility of your software application to authenticate/identify itself before calling CERES PORTAL API. 

A good example of the Client Credential flow from Auth0 can be found [here](https://auth0.com/docs/get-started/authentication-and-authorization-flow/client-credentials-flow). 

In the Auth0 example:

- **M2M app** is your **Software Provider application**
- **Auth0 Tenant** is the Ceres Portal **OAuth2 Service **
- **Your API** is the **Ceres Portal API**

## Steps to Access API Data

1. **Generate credentials for the CERES PORTAL API**

   - You have to authenticate using your software API credentials against our API before you are allowed to query our endpoints and ingest data (see our [minimum requirements document](../Minimum_Requirements.pdf) for details on how often to query).

   - As we use **OAuth2 Client Credential flow** for authentication, you will need a valid **API Client_ID & Secret,** which are used to generate your bearer token.

   - When logged into your CERES PORTAL software account, there is a section where you can generate an **API Client_ID and Secret** . Only one set of credentials is valid at any time and only displayed on screen once. If you have to regenerate credentials, the old credentials stop working immediately.

     <img src="https://user-images.githubusercontent.com/1161583/*********-740ef076-13d9-40d6-9c55-92ed4b9f6136.png">

2. **Familiarise yourself with our [Swagger API Documentation](APIDoco.md)**

   - The [Swagger API Documentation](APIDoco.md) page takes you through each endpoint in our API and explains the data you will receive.

3. **Develop your API code**

   > The endpoints and URL's listed below are for our **test environment only** and as such there may be periods of time when the API endpoint is not available.
   >
   > You will only recevie **production environment** details once your solution has been approved to move into production.
   1. Ensure you have generated your **API Client_ID &amp; Secret**
   2. Use your **API Client_ID & Secret** to generate a Bearer Token:
     * tokenEndpoint: https://testcerestag.auth.ap-southeast-2.amazoncognito.com/oauth2/token

     * Headers:

          * cache-control = no-cache
          * authorization = basic base64, utf-8 encoded (clientID:secret)
          * content-type = application/x-www-form-urlencoded

      * Body(x-www-form-urlencoded):

           * grant_type = client_credentials
           * scope = https://www.cerestag.com/ctms/tags.read

           

           For examples see:

           - [Postman Bearer Token Example](PostmanBearerTokenExample.md)
           
           - [Python 2.7 Example](Py27APIExample.md) - `def Login():`
           - [C# Example](CSharpAPIExample.md) - `private TokenObject GetToken(string clientID, string secret)`
           - Cognito documentation on token endpoints: https://docs.aws.amazon.com/cognito/latest/developerguide/token-endpoint.html

   3. Make a request to the Ceres Portal API using your Bearer Token, to check your authentication is valid:
   
      > There is a default timeout of 3600 seconds on the bearer token
     * URL: https://extapi.test.cerestag.com/FirstContact/CheckAuthentication

     * Headers:

       * Authorization = Bearer ReallyLongBearerTokenThatIsSpecificForYourQueryAndHasATimeout

       For examples see:

       - [Postman API Example](PostmanAPIExample.md)
   
       - [C# example](CSharpAPIExample.md)  `public async Task<string> CallAuthEndpoint()`
       - [Python2.7 Example](Py27APIExample.md)  `def CheckAuthentication(access_token):`
   
   * A successful response is:
       "Authentication Valid **TEST Software Partner**" - where "TEST Software Partner" is the name of your account.
   * Now you know you are authenticated successfully, you can start querying our endpoints and ingest our synthetic data.


4. Begin querying the API. For more details of the different endpoints and methods, visit this [page](APIDoco.md).

  **Configuration examples:**

1. [C#](CSharpAPIExample.md)
2. [C# using RestSharp NuGet Library](RestSharpAPIExample.md)
3. [Python 2.7](Py27APIExample.md)
4. [Postman API Example](PostmanAPIExample.md)
5. [Postman Bearer Token Example](PostmanBearerTokenExample.md)

"use client";
import { useParams } from 'next/navigation';
import { useTagDetails, useTagRecent } from '@/lib/ceres/hooks';
import dynamic from 'next/dynamic';

const ReactECharts = dynamic(() => import('echarts-for-react'), { ssr: false });

export default function TagDetailsPage() {
  const params = useParams<{ esn: string }>();
  const esn = params.esn;

  const details = useTagDetails(esn);
  const recent = useTagRecent(esn);

  const tempSeries = (recent.data?.data ?? []).map((o) => [o.timestamp, o.temperature ?? null]);

  return (
    <div className="p-4 space-y-4">
      <h1 className="text-xl font-semibold">Tag {esn}</h1>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        <InfoCard title="Status" value={details.data?.status ?? '—'} />
        <InfoCard title="Battery" value={details.data?.batteryPercentage != null ? `${details.data.batteryPercentage}%` : '—'} />
        <InfoCard title="Last Seen" value={details.data?.lastSeen ?? '—'} />
      </div>

      <div className="rounded-lg border border-white/10 p-3 bg-white/5">
        <div className="text-sm text-white/70 mb-2">Temperature (Recent)</div>
        <ReactECharts style={{ height: 300 }} option={{
          grid: { left: 40, right: 20, top: 20, bottom: 40 },
          xAxis: { type: 'time', axisLabel: { color: '#b6b6c9' } },
          yAxis: { type: 'value', axisLabel: { color: '#b6b6c9' } },
          dataZoom: [{ type: 'inside' }, { type: 'slider' }],
          series: [{ type: 'line', showSymbol: false, data: tempSeries }],
          tooltip: { trigger: 'axis' },
        }} />
      </div>
    </div>
  );
}

function InfoCard({ title, value }: { title: string; value: string }) {
  return (
    <div className="rounded-lg border border-white/10 p-4 bg-white/5">
      <div className="text-xs text-white/60">{title}</div>
      <div className="text-lg mt-1">{value}</div>
    </div>
  );
}


# CERES TAG Software Partner Documentation

> Welcome to the CERES TAG Software Partner Documentation.
>
> This repository provides an overview of the steps involved and the more technical details of becoming an Approved CERES TAG Software Partner.
>
> These pages **WILL change** over time as our solution rolls out changes.
>
> If you have any questions or feedback, email <NAME_EMAIL>

-----

**The steps involved in becoming an Approved CERES TAG Software Partner are listed below.**

1. **Expression of Interest**

   After an initial Expression of Interest (EOI) meeting, the CERES TAG support team will send onboarding information. This will include:

   - [*Integration Steps*](Integration_Steps.pdf) - This document outlines the steps in becoming an Approved Software Partner in our production environment.
   - *[Minimum Requirement Checklist](Minimum_Requirements.pdf)* - A list of minimum requirements that need to be met to become an approved software partner.

2. **[API Data Discovery](API_Data_Discovery/README.md)**

   During API Data Discovery you should aim to become familiar with how to access and query the synthetic device dataset through our TEST API and determine how you will incorporate the data into your solution. 

   The [minimum requirements checklist](Minimum_Requirements.pdf) is meant as a baseline for expected functionality within your system. Anything your solution does on top of this is an added value for the customer (e.g. analysis, visualisation, intepretation based on multiple data points or a combination of fields from datasets).

3. **Software Partner Agreement**
   
   - A company representative will be sent the Software Partner Agreement as soon a practical so that your company has adequate time to look through the agreement. This agreement covers all parts of integration from EOI to synthetic data access to production release. 
   - Once signed, we can provide access to our synthetic data and test systems and take you through data retrieval and configuration. This access and set up completes step 3 of [integration](Integration_Steps.pdf) and allows you to determine if you wish to continue the integration journey.
   
4. **System 2 System Development**
   - After API Data Discovery, a session will be arranged to go through the system to system communication required to configure [Customer Linkage](Customer_Linkage/README.md), [Webhooks](Webhooks/README.md) and [Historical Data](Historical_Data/README.md) retrieval.
   
5. **Software Partner User Interface (UI) Development**

   - Once you have access to our synthetic data and have been taken through the system to system development, spend some time developing your customer-facing user interface in line with the standards set out in our [Minimum Requirements](Minimum_Requirements.pdf) checklist.

6. **Software Partner Demonstration**
   - To become an Approved CERES TAG Software Partner, you must demonstrate your production ready user Interface to the CERES TAG data team. We will bring a copy of the minimum requirements checklist and go through the items during the meeting.
   - Following your demonstration, you will either be given feedback on how to achieve approval or you will receive approval.

7. **Approval for Production**

   - Once you have demonstrated your solution fulfills our [minimum requirements](Minimum_Requirements.pdf), we will provide you with a production account and provide you with relevant production connection details.
   - You will also receive access to our Software Partner badge and other marketing material and be listed on our integrated Software Partners page. 

8. **Regular Integration Check-ins**

   - Once you are an integrated software partner, we will set up regular checkin meetings for ongoing communication between our companies. This is an opportunity build other B2B relationship and discuss topics such as customer queries, technical issues, planned system outages, upgrades, development opportunities, planned development etc.

-----

Data Flows:

- [**Customer Linkage**](Customer_Linkage/DFD.md)
- [**Historical Data**](Historical_Data/README.md)
- [**API Data Discovery**](API_Data_Discovery/DFD.md)

For a summary of our Integration Steps, visit the [Integration Steps](Integration_Steps.pdf) page.

For more details on our minimum requirements guidelines, visit the [Minimum Requirements](Minimum_Requirements.pdf) page.


using CeresTagClient.Core.Interfaces;
using CeresTagClient.WebhookReceiver.Services;
using Microsoft.AspNetCore.Mvc;

namespace CeresTagClient.WebhookReceiver.Controllers;

[ApiController]
[Route("api/webhooks")]
public class WebhooksController(IConfiguration config, IWebhookSignatureValidator signatureValidator, IMessageQueue queue, ILogger<WebhooksController> logger) : ControllerBase
{
    [HttpPost("receive")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Receive()
    {
        using var reader = new StreamReader(Request.Body);
        var body = await reader.ReadToEndAsync();
        if (string.IsNullOrWhiteSpace(body))
            return Problem(title: "validation_failed", detail: "Empty payload", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");

        var signature = Request.Headers["X-Ceres-Signature"].ToString();
        var sharedSecret = config["WebhookReceiver:SharedSecret"] ?? string.Empty;
        if (string.IsNullOrWhiteSpace(sharedSecret))
            return Problem(title: "config_error", detail: "Receiver shared secret not configured", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");

        if (!signatureValidator.IsValid(body, signature, sharedSecret))
        {
            logger.LogWarning("Invalid webhook signature");
            return Unauthorized();
        }

        queue.Enqueue(new InboundMessage(body, DateTime.UtcNow));
        return Ok();
    }
}


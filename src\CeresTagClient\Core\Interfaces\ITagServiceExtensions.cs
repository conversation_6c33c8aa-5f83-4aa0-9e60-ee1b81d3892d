using System;
using System.Threading;
using System.Threading.Tasks;
using CeresTagClient.Core.Dtos;

namespace CeresTagClient.Core.Interfaces;

/// <summary>
/// Non-breaking alias methods for ITagService to align with CERES TAG naming in docs/specs.
/// Prefer these names moving forward; legacy names remain available on the interface.
/// </summary>
public static class ITagServiceExtensions
{
    /// <summary>
    /// Alias for GetRecentTagDataAsync.
    /// </summary>
    public static Task<RecentTagDataResponse> GetRecentDataAsync(this ITagService service, string esn, DateTime? fromDate = null, int pageSize = 100, int pageNumber = 1, CancellationToken ct = default)
        => service.GetRecentTagDataAsync(esn, fromDate, pageSize, pageNumber, ct);

    /// <summary>
    /// Alias for RequestHistoricalDataAsync.
    /// </summary>
    public static Task<HistoricalRequestResponse> RequestHistoricalAsync(this ITagService service, string esn, DateTime startDate, DateTime endDate, CancellationToken ct = default)
        => service.RequestHistoricalDataAsync(esn, startDate, endDate, ct);

    /// <summary>
    /// Alias for GetHistoricalDataAsync.
    /// </summary>
    public static Task<HistoricalRetrieveResponse> RetrieveHistoricalDataAsync(this ITagService service, string esn, string requestId, int pageSize = 100, int pageNumber = 1, CancellationToken ct = default)
        => service.GetHistoricalDataAsync(esn, requestId, pageSize, pageNumber, ct);
}


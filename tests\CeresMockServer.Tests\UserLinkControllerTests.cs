using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CeresMockServer.Tests;

public class UserLinkControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public UserLinkControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["ConnectionStrings:CeresDb"] = $"Data Source=userlink-{Guid.NewGuid():N}.db"
                };
                config.AddInMemoryCollection(dict);
            });
        });
    }

    [Fact]
    public async Task GetAccess_Requires_GrantId_And_Returns_200_With_Existing_And_Available()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        // Missing -> 400
        var bad = await client.GetAsync("/api/v1/userlink/grants/access");
        Assert.Equal(HttpStatusCode.BadRequest, bad.StatusCode);
        var pd = await bad.Content.ReadFromJsonAsync<ProblemDetails>();
        Assert.Equal("validation_failed", pd!.Title);

        // Valid -> 200
        var good = await client.GetAsync("/api/v1/userlink/grants/access?grantId=cust-abc");
        Assert.Equal(HttpStatusCode.OK, good.StatusCode);
        var payload = await good.Content.ReadFromJsonAsync<UserLinkGrantsAccessResponse>();
        Assert.NotNull(payload);
        Assert.NotNull(payload!.ExistingLinks);
        Assert.NotNull(payload.AvailableLinks);
    }

    [Fact]
    public async Task AddLinks_Validates_And_Returns_Summary()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        // Missing body -> 400
        var bad1 = await client.PostAsJsonAsync("/api/v1/userlink/grants/addlinks", (object?)null);
        Assert.Equal(HttpStatusCode.BadRequest, bad1.StatusCode);
        var pd1 = await bad1.Content.ReadFromJsonAsync<ProblemDetails>();
        Assert.Equal("validation_failed", pd1!.Title);

        // Missing required fields -> 400
        var bad2 = new UserLinkAddLinksRequest { PropertyGrantId = "", Links = [] };
        var bad2Resp = await client.PostAsJsonAsync("/api/v1/userlink/grants/addlinks", bad2);
        Assert.Equal(HttpStatusCode.BadRequest, bad2Resp.StatusCode);

        // Success path
        var req = new UserLinkAddLinksRequest
        {
            PropertyGrantId = "cust-abc",
            Links = new List<UserLinkAddLinkItem>
            {
                new UserLinkAddLinkItem { PropertyId = "prop-1", PropertyName = "Farm 1" },
                new UserLinkAddLinkItem { PropertyId = "" } // invalid -> goes to failed
            }
        };
        var resp = await client.PostAsJsonAsync("/api/v1/userlink/grants/addlinks", req);
        Assert.Equal(HttpStatusCode.OK, resp.StatusCode);
        var dto = await resp.Content.ReadFromJsonAsync<UserLinkAddLinksResponse>();
        Assert.NotNull(dto);
        Assert.Single(dto!.Created);
        Assert.Single(dto.Failed);
    }
}


using System.IdentityModel.Tokens.Jwt;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json.Nodes;
using CeresMockServer.Models;
using CeresTagClient.Application.Services;
using CeresTagClient.Core.Interfaces;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using Xunit;

namespace CeresMockServer.Tests;

public class CustomerLinkageE2ETests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public CustomerLinkageE2ETests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["ConnectionStrings:CeresDb"] = $"Data Source=customer-linkage-e2e-{Guid.NewGuid():N}.db",
                    ["CeresMock:Authentication:Issuer"] = "https://mock.cerestag.com",
                    ["CeresMock:Authentication:Audience"] = "ceres-api",
                    ["CeresMock:Authentication:SigningKey"] = "dev-super-secret-signing-key-change-me",
                    ["CeresMock:CustomerAuth:Issuer"] = "https://mock.cerestag.com/customer",
                    ["CeresMock:CustomerAuth:Audience"] = "ceres-customer",
                    ["CeresMock:CustomerAuth:SigningKey"] = "dev-customer-signing-key-change-me",
                    ["CeresMock:CustomerAuth:TokenExpirySeconds"] = "3600",
                    ["CeresMock:CustomerAuth:DefaultRedirectUri"] = "http://localhost/callback"
                };
                config.AddInMemoryCollection(dict);
            });
        });
    }

    private async Task<HttpClient> CreateAuthorizedClientAsync()
    {
        var client = _factory.CreateClient(new WebApplicationFactoryClientOptions { AllowAutoRedirect = false });
        var tokenResp = await client.PostAsJsonAsync("/oauth2/token", new { client_id = "test-client-1", client_secret = "test-secret-1", grant_type = "client_credentials" });
        tokenResp.EnsureSuccessStatusCode();
        var tokenJson = await tokenResp.Content.ReadFromJsonAsync<JsonObject>();
        var token = tokenJson!["access_token"]!.GetValue<string>();
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        return client;
    }

    private CeresTagClient.Core.Interfaces.ICustomerService CreateCustomerService(Uri baseAddress, string bearerToken)
    {
        var handler = _factory.Server.CreateHandler();
        var services = new ServiceCollection();
        services.AddHttpClient("CeresApi")
            .ConfigureHttpClient(c =>
            {
                c.BaseAddress = baseAddress;
                c.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", bearerToken);
            })
            .ConfigurePrimaryHttpMessageHandler(() => handler);
        services.AddTransient<CeresTagClient.Core.Interfaces.ICustomerService, CeresTagClient.Application.Services.CustomerService>();
        var sp = services.BuildServiceProvider();
        return sp.GetRequiredService<CeresTagClient.Core.Interfaces.ICustomerService>();
    }

    [Fact]
    public async Task StartAuth_Returns_AuthorizationUrl_With_Code_And_State()
    {
        var client = await CreateAuthorizedClientAsync();

        var startReq = new CustomerAuthStartRequest { RedirectUri = "http://localhost/callback", State = "mystate" };
        var startResp = await client.PostAsJsonAsync("/api/v1/customer-linkage/start", startReq);
        Assert.Equal(HttpStatusCode.OK, startResp.StatusCode);
        var startPayload = await startResp.Content.ReadFromJsonAsync<CustomerAuthStartResponse>();
        Assert.NotNull(startPayload);
        Assert.Contains("code=", startPayload!.AuthorizationUrl);
        Assert.Contains("state=mystate", startPayload!.AuthorizationUrl);
    }

    [Fact]
    public async Task Callback_Valid_Code_Returns_Customer_Token_With_JWT_Claims()
    {
        var client = await CreateAuthorizedClientAsync();

        // First start to get the code
        var start = await client.PostAsJsonAsync("/api/v1/customer-linkage/start", new CustomerAuthStartRequest { RedirectUri = "http://localhost/callback", State = "abc123" });
        start.EnsureSuccessStatusCode();
        var startPayload = await start.Content.ReadFromJsonAsync<CustomerAuthStartResponse>();
        var uri = new Uri(startPayload!.AuthorizationUrl);
        var q = System.Web.HttpUtility.ParseQueryString(uri.Query);
        var code = q.Get("code")!;
        var state = q.Get("state")!;

        // Now callback exchange
        var cbResp = await client.PostAsJsonAsync("/api/v1/customer-linkage/callback", new CustomerAuthCallbackRequest { Code = code, State = state });
        Assert.Equal(HttpStatusCode.OK, cbResp.StatusCode);
        var token = await cbResp.Content.ReadFromJsonAsync<CustomerTokenResponse>();
        Assert.NotNull(token);
        Assert.False(string.IsNullOrWhiteSpace(token!.AccessToken));
        Assert.True(token!.ExpiresIn > 0);
        Assert.False(string.IsNullOrWhiteSpace(token!.CustomerId));

        // Validate customer JWT claims from the token returned
        var handler = new JwtSecurityTokenHandler();
        var jwt = handler.ReadJwtToken(token!.AccessToken);
        var sub = jwt.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Sub)?.Value;
        var scope = jwt.Claims.FirstOrDefault(c => c.Type == "scope")?.Value;
        var custId = jwt.Claims.FirstOrDefault(c => c.Type == "customer_id")?.Value;
        Assert.Equal(token.CustomerId, custId);
        Assert.Equal("customer", scope);
        Assert.Equal(custId, sub);
    }

    [Fact]
    public async Task CustomerService_EndToEnd_Works_Against_MockServer()
    {
        // Obtain token and base address
        var http = _factory.CreateClient(new WebApplicationFactoryClientOptions { AllowAutoRedirect = false });
        var tokenResp = await http.PostAsJsonAsync("/oauth2/token", new { client_id = "test-client-1", client_secret = "test-secret-1", grant_type = "client_credentials" });
        tokenResp.EnsureSuccessStatusCode();
        var tokenJson = await tokenResp.Content.ReadFromJsonAsync<JsonObject>();
        var bearer = tokenJson!["access_token"]!.GetValue<string>();
        var customerService = CreateCustomerService(http.BaseAddress!, bearer);
        var state = Guid.NewGuid().ToString("N");

        var start = await customerService.StartCustomerAuthAsync("http://localhost/callback", state);
        Assert.NotNull(start);
        Assert.Contains("code=", start!.AuthorizationUrl);
        Assert.Contains($"state={state}", start!.AuthorizationUrl);
        var uri = new Uri(start!.AuthorizationUrl);
        var q = System.Web.HttpUtility.ParseQueryString(uri.Query);
        var code = q.Get("code")!;

        var token = await customerService.ExchangeAuthorizationCodeAsync(code, state);
        Assert.NotNull(token);
        Assert.False(string.IsNullOrWhiteSpace(token!.AccessToken));
        Assert.False(string.IsNullOrWhiteSpace(token!.CustomerId));
    }

    [Fact]
    public async Task Callback_Invalid_Base64_Code_Returns_400_ValidationFailed()
    {
        var client = _factory.CreateClient();
        var cbResp = await client.PostAsJsonAsync("/api/v1/customer-linkage/callback", new CustomerAuthCallbackRequest { Code = "not-base64", State = "x" });
        Assert.Equal(HttpStatusCode.BadRequest, cbResp.StatusCode);
        var problem = await cbResp.Content.ReadFromJsonAsync<JsonObject>();
        Assert.Equal("validation_failed", problem!["title"]!.GetValue<string>());
        Assert.Equal("invalid_code", problem!["detail"]!.GetValue<string>());
    }

    [Fact]
    public async Task Callback_Invalid_Code_Format_Returns_400_ValidationFailed()
    {
        var client = _factory.CreateClient();
        var bad = Convert.ToBase64String(Encoding.UTF8.GetBytes("garbage"));
        var cbResp = await client.PostAsJsonAsync("/api/v1/customer-linkage/callback", new CustomerAuthCallbackRequest { Code = bad, State = "x" });
        Assert.Equal(HttpStatusCode.BadRequest, cbResp.StatusCode);
        var problem = await cbResp.Content.ReadFromJsonAsync<JsonObject>();
        Assert.Equal("validation_failed", problem!["title"]!.GetValue<string>());
        Assert.Equal("invalid_code", problem!["detail"]!.GetValue<string>());
    }
}


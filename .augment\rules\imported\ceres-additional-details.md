---
type: "agent_requested"
description: "Example description"
---

# CERES TAG Integration - Additional Story Details & Technical Specifications

## Technical Implementation Details Per Story

### Mock Server Implementation Specifics

#### Epic 1 - Foundation Stories Technical Details

**Story 1.1 Mock Server Project Setup - Technical Specs:**
```csharp
// Project Structure
/CeresMockServer
  /Controllers
    - TokenController.cs
    - TagController.cs
    - WebhookController.cs
    - PropertyController.cs
  /Services
    - IAuthenticationService.cs
    - IWebhookService.cs
    - IDataGeneratorService.cs
  /Models
    - Tag.cs
    - Property.cs
    - Customer.cs
    - Observation.cs
  /Data
    - CeresContext.cs
    - SeedData.cs
  /Middleware
    - DelaySimulationMiddleware.cs
    - RateLimitingMiddleware.cs
```

**Story 1.2 SQLite Database - Entity Models:**
```csharp
public class Tag
{
    public string ESN { get; set; }  // Electronic Serial Number
    public string Brand { get; set; } // CeresTrace, CeresWild, CeresRanch
    public string FirmwareVersion { get; set; }
    public DateTime ActivationDate { get; set; }
    public int BatteryPercentage { get; set; }
    public string PropertyId { get; set; }
    public string CustomerId { get; set; }
}

public class Observation
{
    public int Id { get; set; }
    public string ESN { get; set; }
    public DateTime Timestamp { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string Activity { get; set; } // grazing, walking, resting
    public double Temperature { get; set; }
}
```

#### Epic 2 - OAuth2 Stories Technical Details

**Story 2.1 OAuth2 Token Endpoint - Request/Response:**
```http
POST /oauth2/token
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials&
client_id={client_id}&
client_secret={client_secret}

Response:
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 7200
}
```

**Story 2.2 Token Expiry - JWT Claims Structure:**
```json
{
  "iss": "https://mock.cerestag.com",
  "sub": "client_id",
  "aud": "ceres-api",
  "exp": 1234567890,
  "iat": 1234567890,
  "nbf": 1234567890,
  "jti": "unique-token-id",
  "scope": "read write"
}
```

#### Epic 3 - API Endpoints Technical Details

**Story 3.1 TagDetails Response Format:**
```json
{
  "esn": "500000000000001",
  "brand": "CeresTrace",
  "firmwareVersion": "3.2.1",
  "activationDate": "2024-01-15T08:00:00Z",
  "batteryPercentage": 85,
  "status": "active",
  "property": {
    "id": "prop-123",
    "name": "North Paddock"
  },
  "lastSeen": "2024-11-20T14:30:00Z"
}
```

**Story 3.2 RecentTagData Response Format:**
```json
{
  "data": [
    {
      "esn": "500000000000001",
      "timestamp": "2024-11-20T14:30:00Z",
      "latitude": -27.4698,
      "longitude": 153.0251,
      "activity": "grazing",
      "temperature": 38.5,
      "altitude": 45.2,
      "hdop": 1.2
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 100,
    "totalRecords": 250
  }
}
```

**Story 3.3 Historical Data Request/Retrieve Pattern:**
```csharp
// Step 1: Request Historical Data
POST /api/v1/historical/request
{
  "esn": "500000000000001",
  "fromDate": "2024-01-01T00:00:00Z",
  "toDate": "2024-11-20T00:00:00Z"
}
Response: { "requestId": "req-456789" }

// Step 2: Check Status
GET /api/v1/historical/status/req-456789
Response: { "status": "processing" | "ready" | "failed" }

// Step 3: Retrieve Data
GET /api/v1/historical/retrieve/req-456789
```

#### Epic 4 - Webhook Technical Details

**Story 4.1 Alert Webhook Payload:**
```json
{
  "webhookType": "alert",
  "timestamp": "2024-11-20T15:00:00Z",
  "data": {
    "esn": "500000000000001",
    "alertType": "high_activity",
    "severity": "warning",
    "location": {
      "latitude": -27.4698,
      "longitude": 153.0251
    },
    "message": "Unusual activity detected",
    "threshold": 120,
    "actualValue": 145
  }
}
```

**Story 4.2 PFI Webhook Payload:**
```json
{
  "webhookType": "pfi_summary",
  "date": "2024-11-20",
  "data": {
    "esn": "500000000000001",
    "grazingMinutes": 720,
    "walkingMinutes": 180,
    "restingMinutes": 420,
    "ruminatingMinutes": 120,
    "drinkingMinutes": 15,
    "otherMinutes": 5,
    "pastureIntakeKg": 12.5,
    "methaneGrams": 285
  }
}
```

**Story 4.3 HMAC Signature Generation:**
```csharp
public string GenerateHmacSignature(string payload, string secret)
{
    using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secret));
    var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(payload));
    return Convert.ToBase64String(hash);
}

// Add to webhook headers
request.Headers.Add("X-Ceres-Signature", signature);
```

### Client Implementation Specifics

#### Epic 7 - Foundation Technical Details

**Story 7.1 Client Library Structure:**
```csharp
/CeresTagClient
  /Core
    /Entities
    /Interfaces
    /Exceptions
  /Infrastructure
    /Http
    /Authentication
    /Data
  /Application
    /Services
    /Handlers
    /Validators
```

**Story 7.2 HTTP Client with Polly:**
```csharp
services.AddHttpClient<ICeresApiClient>("CeresApi")
    .ConfigureHttpClient(client =>
    {
        client.BaseAddress = new Uri(configuration["CeresApi:BaseUrl"]);
        client.Timeout = TimeSpan.FromSeconds(30);
    })
    .AddPolicyHandler(GetRetryPolicy())
    .AddPolicyHandler(GetCircuitBreakerPolicy());

private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
{
    return HttpPolicyExtensions
        .HandleTransientHttpError()
        .WaitAndRetryAsync(
            3,
            retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
            onRetry: (outcome, timespan, retryCount, context) =>
            {
                Log.Warning("Retry {RetryCount} after {Timespan}s", retryCount, timespan);
            });
}
```

#### Epic 8 - Authentication Technical Details

**Story 8.1 Authentication Service:**
```csharp
public interface IAuthenticationService
{
    Task<string> GetAccessTokenAsync();
    Task<bool> RefreshTokenAsync();
}

public class AuthenticationService : IAuthenticationService
{
    private readonly SemaphoreSlim _semaphore = new(1, 1);
    private TokenResponse _cachedToken;
    
    public async Task<string> GetAccessTokenAsync()
    {
        await _semaphore.WaitAsync();
        try
        {
            if (_cachedToken == null || IsExpired(_cachedToken))
            {
                _cachedToken = await RequestNewTokenAsync();
            }
            return _cachedToken.AccessToken;
        }
        finally
        {
            _semaphore.Release();
        }
    }
}
```

#### Epic 9 - API Services Technical Details

**Story 9.1 Tag Service Interface:**
```csharp
public interface ITagService
{
    Task<TagDetails> GetTagDetailsAsync(string esn, CancellationToken ct = default);
    Task<RecentDataResponse> GetRecentTagDataAsync(string esn, DateTime? fromDate = null, CancellationToken ct = default);
    Task<string> RequestHistoricalDataAsync(HistoricalDataRequest request, CancellationToken ct = default);
    Task<HistoricalDataStatus> GetHistoricalDataStatusAsync(string requestId, CancellationToken ct = default);
    Task<HistoricalDataResponse> RetrieveHistoricalDataAsync(string requestId, CancellationToken ct = default);
}
```

**Story 9.3 FluentValidation Example:**
```csharp
public class HistoricalDataRequestValidator : AbstractValidator<HistoricalDataRequest>
{
    public HistoricalDataRequestValidator()
    {
        RuleFor(x => x.ESN)
            .NotEmpty()
            .Matches(@"^\d{15}$")
            .WithMessage("ESN must be 15 digits");
            
        RuleFor(x => x.FromDate)
            .LessThan(x => x.ToDate)
            .WithMessage("FromDate must be before ToDate");
            
        RuleFor(x => x.ToDate)
            .LessThanOrEqualTo(DateTime.UtcNow)
            .WithMessage("ToDate cannot be in the future");
    }
}
```

#### Epic 10 - Webhook Processing Technical Details

**Story 10.1 Webhook Controller:**
```csharp
[ApiController]
[Route("api/webhooks")]
public class WebhookController : ControllerBase
{
    [HttpPost("receive")]
    public async Task<IActionResult> ReceiveWebhook(
        [FromHeader(Name = "X-Ceres-Signature")] string signature,
        [FromBody] string payload)
    {
        if (!ValidateSignature(payload, signature))
            return Unauthorized();
            
        await _messageQueue.EnqueueAsync(payload);
        return Ok(); // Return immediately
    }
    
    private bool ValidateSignature(string payload, string signature)
    {
        var expectedSignature = GenerateHmacSignature(payload, _webhookSecret);
        return signature == expectedSignature;
    }
}
```

**Story 10.3 Webhook Handler Strategy:**
```csharp
public interface IWebhookHandler
{
    string WebhookType { get; }
    Task HandleAsync(WebhookPayload payload);
}

public class AlertWebhookHandler : IWebhookHandler
{
    public string WebhookType => "alert";
    
    public async Task HandleAsync(WebhookPayload payload)
    {
        var alert = JsonSerializer.Deserialize<AlertData>(payload.Data);
        // Process alert
        await _alertService.ProcessAlertAsync(alert);
    }
}

public class WebhookProcessor
{
    private readonly Dictionary<string, IWebhookHandler> _handlers;
    
    public async Task ProcessWebhookAsync(string payload)
    {
        var webhook = JsonSerializer.Deserialize<WebhookPayload>(payload);
        if (_handlers.TryGetValue(webhook.WebhookType, out var handler))
        {
            await handler.HandleAsync(webhook);
        }
    }
}
```

#### Epic 11 - Data Sync Technical Details

**Story 11.1 Incremental Sync Service:**
```csharp
public class SyncService : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var lastSyncTime = await _syncStateRepository.GetLastSyncTimeAsync();
            var newData = await _tagService.GetRecentTagDataAsync(
                fromDate: lastSyncTime,
                ct: stoppingToken);
                
            await ProcessAndStoreDataAsync(newData);
            await _syncStateRepository.UpdateLastSyncTimeAsync(DateTime.UtcNow);
            
            await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
        }
    }
}
```

#### Epic 12 - Business Logic Technical Details

**Story 12.1 Alert Processing Rules:**
```csharp
public class AlertProcessor
{
    public AlertPriority ClassifyAlert(Alert alert)
    {
        return alert.Type switch
        {
            "mortality" => AlertPriority.Critical,
            "no_activity" when alert.Duration > TimeSpan.FromHours(12) => AlertPriority.High,
            "high_activity" when alert.Value > alert.Threshold * 1.5 => AlertPriority.High,
            "geofence_breach" => AlertPriority.Medium,
            _ => AlertPriority.Low
        };
    }
}
```

**Story 12.2 PFI Analytics Calculations:**
```csharp
public class PfiAnalytics
{
    public PfiTrend CalculateTrend(List<PfiSummary> summaries)
    {
        var averageIntake = summaries.Average(s => s.PastureIntakeKg);
        var trend = new PfiTrend
        {
            AverageIntake = averageIntake,
            GrazingEfficiency = summaries.Average(s => s.GrazingMinutes / 1440.0),
            MethaneOutput = summaries.Average(s => s.MethaneGrams),
            ActivityDistribution = CalculateActivityDistribution(summaries)
        };
        return trend;
    }
}
```

## Database Schema Details

### Mock Server SQLite Schema

```sql
-- Tags Table
CREATE TABLE Tags (
    ESN TEXT PRIMARY KEY,
    Brand TEXT NOT NULL,
    FirmwareVersion TEXT,
    ActivationDate DATETIME,
    BatteryPercentage INTEGER,
    PropertyId TEXT,
    CustomerId TEXT,
    Status TEXT,
    FOREIGN KEY (PropertyId) REFERENCES Properties(Id),
    FOREIGN KEY (CustomerId) REFERENCES Customers(Id)
);

-- Observations Table
CREATE TABLE Observations (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ESN TEXT NOT NULL,
    Timestamp DATETIME NOT NULL,
    Latitude REAL,
    Longitude REAL,
    Activity TEXT,
    Temperature REAL,
    Altitude REAL,
    HDOP REAL,
    FOREIGN KEY (ESN) REFERENCES Tags(ESN)
);

-- PFI Summaries Table
CREATE TABLE PfiSummaries (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ESN TEXT NOT NULL,
    Date DATE NOT NULL,
    GrazingMinutes INTEGER,
    WalkingMinutes INTEGER,
    RestingMinutes INTEGER,
    RuminatingMinutes INTEGER,
    DrinkingMinutes INTEGER,
    OtherMinutes INTEGER,
    PastureIntakeKg REAL,
    MethaneGrams REAL,
    FOREIGN KEY (ESN) REFERENCES Tags(ESN),
    UNIQUE(ESN, Date)
);

-- Alerts Table
CREATE TABLE Alerts (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ESN TEXT NOT NULL,
    AlertType TEXT NOT NULL,
    Timestamp DATETIME NOT NULL,
    Severity TEXT,
    Message TEXT,
    Acknowledged BOOLEAN DEFAULT 0,
    AcknowledgedBy TEXT,
    AcknowledgedAt DATETIME,
    FOREIGN KEY (ESN) REFERENCES Tags(ESN)
);

-- Webhook Subscriptions Table
CREATE TABLE WebhookSubscriptions (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    CustomerId TEXT NOT NULL,
    WebhookType TEXT NOT NULL,
    EndpointUrl TEXT NOT NULL,
    Secret TEXT NOT NULL,
    Active BOOLEAN DEFAULT 1,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (CustomerId) REFERENCES Customers(Id)
);
```

### Client SQLite Schema

```sql
-- Cached Tags Table
CREATE TABLE CachedTags (
    ESN TEXT PRIMARY KEY,
    JsonData TEXT NOT NULL,
    LastUpdated DATETIME NOT NULL,
    ExpiresAt DATETIME
);

-- Sync State Table
CREATE TABLE SyncState (
    Id INTEGER PRIMARY KEY,
    EntityType TEXT NOT NULL,
    LastSyncTime DATETIME NOT NULL,
    Status TEXT,
    RecordCount INTEGER,
    ErrorMessage TEXT
);

-- Processed Webhooks Table
CREATE TABLE ProcessedWebhooks (
    Id TEXT PRIMARY KEY,
    WebhookType TEXT NOT NULL,
    ReceivedAt DATETIME NOT NULL,
    ProcessedAt DATETIME,
    Status TEXT,
    PayloadHash TEXT,
    ErrorMessage TEXT
);

-- Token Cache Table
CREATE TABLE TokenCache (
    Id INTEGER PRIMARY KEY,
    AccessToken TEXT NOT NULL,
    TokenType TEXT,
    ExpiresAt DATETIME NOT NULL,
    RefreshToken TEXT,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Configuration Files

### Mock Server appsettings.json
```json
{
  "CeresMock": {
    "Authentication": {
      "TokenExpiry": 7200,
      "TestClients": [
        {
          "ClientId": "test-client-1",
          "ClientSecret": "test-secret-1"
        }
      ]
    },
    "Webhooks": {
      "DefaultSecret": "webhook-secret-key",
      "RetryAttempts": 3,
      "RetryDelaySeconds": [5, 15, 60]
    },
    "DataGeneration": {
      "DefaultMovementSpeed": 2.5,
      "AlertThresholds": {
        "HighActivity": 120,
        "NoActivity": 360
      }
    },
    "ResponseSimulation": {
      "DefaultDelayMs": 0,
      "RandomDelayMax": 500
    }
  }
}
```

### Client appsettings.json
```json
{
  "CeresApi": {
    "BaseUrl": "https://api.cerestag.com",
    "TokenEndpoint": "/oauth2/token",
    "ClientId": "your-client-id",
    "ClientSecret": "your-client-secret",
    "WebhookSecret": "webhook-secret-key"
  },
  "Sync": {
    "IntervalMinutes": 5,
    "BatchSize": 100,
    "MaxRetries": 3
  },
  "Database": {
    "ConnectionString": "Data Source=ceres_client.db"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "CeresTagClient": "Debug"
    }
  }
}
```

## Testing Scenarios

### Integration Test Scenarios

1. **Authentication Flow**
   - Valid credentials → Token received
   - Invalid credentials → 401 Unauthorized
   - Expired token → Automatic refresh
   - Token near expiry → Proactive refresh

2. **Data Synchronization**
   - Initial full sync
   - Incremental updates
   - Handle gaps in data
   - Recovery from failures

3. **Webhook Processing**
   - Valid signature → Process webhook
   - Invalid signature → Reject webhook
   - Duplicate webhook → Idempotent handling
   - Failed processing → Retry logic

4. **Alert Management**
   - High activity alert generation
   - No activity detection
   - Alert acknowledgment
   - Alert aggregation

5. **Historical Data**
   - Request large date range
   - Poll for completion
   - Retrieve in batches
   - Handle timeout scenarios

## Performance Requirements

### Mock Server Performance
- Response time: < 200ms for simple queries
- Webhook delivery: < 5 seconds
- Concurrent connections: 100+
- Data generation: 1000 tags with observations

### Client Performance
- API call timeout: 30 seconds
- Webhook processing: < 1 second to return 200 OK
- Batch processing: 1000 records/minute
- Memory usage: < 500MB for normal operations

## Security Considerations

### Mock Server Security
- JWT token validation on protected endpoints
- HMAC signature on all webhooks
- Rate limiting per client
- SQL injection prevention
- CORS configuration

### Client Security
- Secure credential storage
- Token encryption at rest
- Certificate pinning option
- Audit logging for sensitive operations
- Input validation on all endpoints
# CERES PORTAL Software Account

As a part of [API Data Discovery](API_Data_Discovery/README.md), you will have one or more software accounts created in the CERES PORTAL. 
There are 3 main sections in this account: Customer Linkage, API Credentials and Webhooks (see screenshot below).

## Customer Linkage

[Customer Linkage](Customer_Linkage/README.md) requires you to provide 3 variables (denoted 1-3 in the image below)

**(1) Software Partner Customer Authentication Endpoint**

- This is the endpoint your customers would use to verify they have an account in your solution.
- Must be HTTPS. 
- The **CERES PORTAL Auth Client ID** (detailed below), response_type, state variable and a redirect_uri to the signin callback will be appended when the endpoint is launched as part of Customer Linkage. For more details see step 4.1 in [Customer Linkage](Customer_Linkage/README.md)

**(2) Software Partner Customer Account Identifiers Endpoint**

- Must be HTTPS. 
- The endpoint where the CERES PORTAL requests customer account identifiers from your system using a customer's access_token.

**(3) Ceres Portal Auth Client ID**

- An ID the CERES PORTAL uses to authenticate a **GET** request to the **Software Partner Customer Authentication Endpoint** when it is launched.

For more details on where these are used, look at the [Customer Linkage Data Flow Diagrams](Customer_Linkage/DFD.md) and [Customer Linkage](Customer_Linkage/README.md)

## API Credentials

Generates a set of OAuth2 credentials (a Client ID & Secret) for API access. **These credentials are solution wide**, so be mindful if you have more than one user with a CERES PORTAL Software Account as regenerating these credentials will void the previous credentials for API access. 

To test out these credentials and the data you can access, you can go through the steps, starting with "Authorising the Swagger client" on [API Documentation](API_Data_Discovery/APIDoco.md).

## Webhooks

For more details on webhooks and how they are used in the CERES PORTAL, visit [Webhooks](Webhooks/README.md)

<kbd><img src="https://user-images.githubusercontent.com/1161583/*********-99e5eedc-9d84-4f8a-b033-1d6eeebc24d2.png" /></kbd>


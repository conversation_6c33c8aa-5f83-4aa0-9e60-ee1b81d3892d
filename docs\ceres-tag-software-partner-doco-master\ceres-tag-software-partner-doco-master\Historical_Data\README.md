# Historical Data

The process involved in requesting and retrieving a complete data history of the devices you have access to. You can now query the HistoricalTagData Endpoint for more than 100 devices in a single request. The system will automatically batch the devices into groups of 100 and return multiple historyQueryIds, one for each batch, ensuring efficient processing and retrieval of historical data for large device sets.


## 1. Set up your webhook

You must have a valid, tested, webhook configured to be able to retrieve your tag history. This will be confirmed by the CERES PORTAL system before even allowing your solution to request historical device data.

To find out more about setting up your webhook, see the section on [Webhooks](../Webhooks/README.md)



<img src="https://user-images.githubusercontent.com/1161583/175872189-e1e0e385-7316-46b0-a344-699607c2c59f.jpg">

## 2. Check Authentication is Valid

Before you can request the histroy of a device, you must ensure you request is authenticated. For more details on how to access the API, see [API Data Discovery](../API_Data_Discovery/README.md)

<img src="https://user-images.githubusercontent.com/1161583/175872205-57280f2b-bebc-485c-9926-a9377f54df33.jpg">

## 3. Request Tag Histories

Submit a POST request list of the device (Eletronic Serial Numbers) ESN's that you want to get a complete data history of, to the HistoricalTagData Request endpoint. 

When submitting a POST request, you must specify one or more ESN's (maximum 100 at once) and you can choose between a **Standard data packet ("standard")** or a **PFI data packet ("pfi_summary")** as indicated in the **dataType** field. If no specification is made, the system will default to the **Standard data** packet. Note that the dataType field is optional.

If not successful, the CERES PORTAL will respond with an error code and explaination.

If successful, the CERES PORTAL will respond with a 200 and a list of successful ESN's submitted in the structure below. The CERES PORTAL will then start the process of retrieving all valid device data from your request.

<img src="https://github.com/user-attachments/assets/39434ff0-1c15-496d-b3f0-755e88f07178">



## 4. History Ready Notification

Once all device data history has been retrieved, a historyQueryId is assigned to each device and this ID is sent to your Historical webhook. Once sent, your solution must repond with a **status 200**.

 **If a 200 is not received** acknowledging receipt of the ID, the CERES PORTAL assumes it has not been received, waits **double the amount** of time since the last request was sent (e.g. 1min, 2min, 5min, 10min) and then sends another notification, **up to 3 hours** after the initial notification, before no more notifications are sent. 

<img src="https://user-images.githubusercontent.com/1161583/175872216-d8bcffa2-6559-4e80-a567-3fb7127369c7.jpg">

## 5. Retrieve Tag History

Once your solution has received the histroyQueryId, this can be used to retrieve a complete device history.

<img src="https://user-images.githubusercontent.com/1161583/175872224-8f2afaa5-51a2-4a93-be42-e361bb8ac2b5.jpg">
using System.Net.Http.Headers;
using System.Net.Http.Json;
using CeresMockServer;
using CeresMockServer.Data;
using CeresMockServer.Options;
using CeresMockServer.Services;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CeresMockServer.Tests;

public class AlertEvaluationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public AlertEvaluationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["ConnectionStrings:CeresDb"] = $"Data Source=alerts-{Guid.NewGuid():N}.db",
                    ["CeresMock:DataGen:PaddockRadiusMeters"] = "1000"
                };
                config.AddInMemoryCollection(dict);
            });
        });
    }

    private async Task<(HttpClient client, CeresDbContext db)> PrepareAsync()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        var scope = _factory.Services.CreateScope();
        var db = scope.ServiceProvider.GetRequiredService<CeresDbContext>();
        return (client, db);
    }

    [Fact]
    public async Task HighActivity_SlidingWindow_Detected()
    {
        var (client, db) = await PrepareAsync();
        db.Tags.Add(new Tag { Esn = "200000000000001", PropertyId = "p", PropertyName = "P" });
        await db.SaveChangesAsync();

        // Generate walking-heavy last 30 minutes by inserting observations directly
        var now = DateTime.UtcNow;
        for (var t = now.AddMinutes(-30); t <= now; t = t.AddMinutes(1))
            db.Observations.Add(new Observation { Esn = "200000000000001", Timestamp = t, Latitude = -27, Longitude = 151, Activity = t.Minute % 10 == 0 ? "grazing" : "walking", Temperature = 20 });
        await db.SaveChangesAsync();

        using var scopeSvc = _factory.Services.CreateScope();
        var svc = scopeSvc.ServiceProvider.GetRequiredService<IAlertEvaluator>();
        var alerts = await svc.EvaluateAsync("200000000000001");
        Assert.Contains(alerts, a => a.AlertType == "high_activity");
    }

    [Fact]
    public async Task NoActivity_SlidingWindow_Detected()
    {
        var (client, db) = await PrepareAsync();
        db.Tags.Add(new Tag { Esn = "200000000000002", PropertyId = "p", PropertyName = "P" });
        await db.SaveChangesAsync();

        var now = DateTime.UtcNow;
        // 60 minutes with mostly resting
        for (var t = now.AddMinutes(-60); t <= now; t = t.AddMinutes(1))
            db.Observations.Add(new Observation { Esn = "200000000000002", Timestamp = t, Latitude = -27, Longitude = 151, Activity = t.Minute % 12 == 0 ? "walking" : "resting", Temperature = 20 });
        await db.SaveChangesAsync();

        using var scopeSvc2 = _factory.Services.CreateScope();
        var svc = scopeSvc2.ServiceProvider.GetRequiredService<IAlertEvaluator>();
        var alerts = await svc.EvaluateAsync("200000000000002");
        Assert.Contains(alerts, a => a.AlertType == "no_activity");
    }

    [Fact]
    public async Task Geofence_Breach_Detected()
    {
        var (client, db) = await PrepareAsync();
        db.Tags.Add(new Tag { Esn = "200000000000003", PropertyId = "p", PropertyName = "P" });
        await db.SaveChangesAsync();

        var now = DateTime.UtcNow;
        // Outside paddock by far
        db.Observations.Add(new Observation { Esn = "200000000000003", Timestamp = now, Latitude = -10, Longitude = 10, Activity = "walking", Temperature = 20 });
        await db.SaveChangesAsync();

        using var scopeSvc3 = _factory.Services.CreateScope();
        var svc = scopeSvc3.ServiceProvider.GetRequiredService<IAlertEvaluator>();
        var alerts = await svc.EvaluateAsync("200000000000003");
        Assert.Contains(alerts, a => a.AlertType == "geofence_breach");
    }

    [Fact]
    public async Task Low_Battery_Detected()
    {
        var (client, db) = await PrepareAsync();
        db.Tags.Add(new Tag { Esn = "200000000000004", PropertyId = "p", PropertyName = "P", BatteryPercentage = 15 });
        await db.SaveChangesAsync();

        using var scopeSvc4 = _factory.Services.CreateScope();
        var svc = scopeSvc4.ServiceProvider.GetRequiredService<IAlertEvaluator>();
        var alerts = await svc.EvaluateAsync("200000000000004");
        Assert.Contains(alerts, a => a.AlertType == "low_battery");
    }
}


using CeresTagClient.Core.Requests;
using FluentValidation;

namespace CeresTagClient.Application.Validation;

public class TagDetailsRequestValidator : AbstractValidator<TagDetailsRequest>
{
    public TagDetailsRequestValidator()
    {
        RuleFor(x => x.ESN)
            .NotEmpty()
            .Matches("^\\d{15}$").WithMessage("ESN must be exactly 15 numeric digits");
    }
}

public class RecentTagDataRequestValidator : AbstractValidator<RecentTagDataRequest>
{
    public RecentTagDataRequestValidator()
    {
        RuleFor(x => x.ESN)
            .NotEmpty()
            .Matches("^\\d{15}$").WithMessage("ESN must be exactly 15 numeric digits");

        RuleFor(x => x.PageSize)
            .GreaterThan(0)
            .LessThanOrEqualTo(1000);

        RuleFor(x => x.PageNumber)
            .GreaterThan(0);
    }
}


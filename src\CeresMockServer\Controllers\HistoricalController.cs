using System.Text.RegularExpressions;
using CeresMockServer.Data;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CeresMockServer.Controllers;

[ApiController]
[Authorize]
[Route("api/v1/tags/{esn}/historical")]
public class HistoricalController(CeresDbContext db, ILogger<HistoricalController> logger) : ControllerBase
{
    private static readonly Regex EsnRegex = new("^\\d{15}$");

    /// <summary>
    /// Requests historical data for a tag over a date range. Returns an opaque requestId to poll for data.
    /// </summary>
    /// <param name="esn">The tag ESN (15 digits).</param>
    /// <param name="dto">The historical range request.</param>
    /// <response code="200">Request accepted with a requestId.</response>
    /// <response code="400">Validation failed.</response>
    /// <response code="401">Unauthorized.</response>
    /// <response code="404">Tag not found.</response>
    [HttpPost("request")]
    [ProducesResponseType(typeof(HistoricalRequestResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> RequestHistorical([FromRoute] string esn, [FromBody] HistoricalRequestDto dto, CancellationToken ct)
    {
        if (!EsnRegex.IsMatch(esn))
            return Problem(title: "validation_failed", detail: "ESN must be exactly 15 numeric digits", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
        if (dto == null)
            return Problem(title: "validation_failed", detail: "Body is required", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
        if (dto.EndDate <= dto.StartDate)
            return Problem(title: "validation_failed", detail: "endDate must be greater than startDate", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
        if ((dto.EndDate - dto.StartDate).TotalDays > 31)
            return Problem(title: "validation_failed", detail: "Requested range must not exceed 31 days", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");

        var tagExists = await db.Tags.AsNoTracking().AnyAsync(t => t.Esn == esn, ct);
        if (!tagExists)
        {
            return Problem(title: "tag_not_found", detail: $"Tag '{esn}' not found", statusCode: StatusCodes.Status404NotFound, type: "about:blank");
        }

        // Mock behavior: generate a deterministic requestId for this esn+range (use '|' delimiter to avoid ISO colons)
        var requestId = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes($"{esn}|{dto.StartDate:O}|{dto.EndDate:O}")).TrimEnd('=');
        return Ok(new HistoricalRequestResponse { RequestId = requestId });
    }

    /// <summary>
    /// Retrieves historical data for a previously requested range using the requestId.
    /// </summary>
    /// <param name="esn">The tag ESN (15 digits).</param>
    /// <param name="requestId">Opaque request identifier from the request step.</param>
    /// <param name="pageSize">Page size (1-1000).</param>
    /// <param name="pageNumber">Page number (>=1).</param>
    /// <response code="200">Returns data with pagination.</response>
    /// <response code="400">Validation failed or invalid requestId.</response>
    /// <response code="401">Unauthorized.</response>
    /// <response code="404">Tag not found.</response>
    [HttpGet("{requestId}")]
    [ProducesResponseType(typeof(HistoricalRetrieveResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> RetrieveHistorical([FromRoute] string esn, [FromRoute] string requestId, [FromQuery] int pageSize = 100, [FromQuery] int pageNumber = 1, CancellationToken ct = default)
    {
        if (!EsnRegex.IsMatch(esn))
            return Problem(title: "validation_failed", detail: "ESN must be exactly 15 numeric digits", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
        if (pageSize <= 0 || pageSize > 1000 || pageNumber <= 0)
            return Problem(title: "validation_failed", detail: "Invalid pagination parameters", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");

        var tagExists = await db.Tags.AsNoTracking().AnyAsync(t => t.Esn == esn, ct);
        if (!tagExists)
        {
            return Problem(title: "tag_not_found", detail: $"Tag '{esn}' not found", statusCode: StatusCodes.Status404NotFound, type: "about:blank");
        }

        // Decode requestId back to range
        DateTime start, end;
        try
        {
            var bytes = Convert.FromBase64String(PadBase64(requestId));
            var token = System.Text.Encoding.UTF8.GetString(bytes);
            var parts = token.Split('|');
            if (parts.Length != 3 || parts[0] != esn) throw new Exception("invalid requestId");
            start = DateTime.Parse(parts[1], null, System.Globalization.DateTimeStyles.RoundtripKind);
            end = DateTime.Parse(parts[2], null, System.Globalization.DateTimeStyles.RoundtripKind);
        }
        catch
        {
            return Problem(title: "validation_failed", detail: "Invalid requestId", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
        }

        // Mock immediate readiness
        var query = db.Observations.AsNoTracking().Where(o => o.Esn == esn && o.Timestamp >= start && o.Timestamp <= end);
        var total = await query.CountAsync(ct);
        var data = await query
            .OrderBy(o => o.Timestamp)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .Select(o => new RecentObservation
            {
                Esn = o.Esn,
                Timestamp = o.Timestamp,
                Latitude = o.Latitude,
                Longitude = o.Longitude,
                Activity = o.Activity,
                Temperature = o.Temperature,
                Altitude = o.Altitude,
                Hdop = o.Hdop
            })
            .ToListAsync(ct);

        var response = new HistoricalRetrieveResponse
        {
            Status = "ready",
            Data = data,
            Pagination = new PaginationMeta
            {
                TotalCount = total,
                PageSize = pageSize,
                PageNumber = pageNumber,
                HasNextPage = (pageNumber * pageSize) < total
            }
        };
        return Ok(response);
    }

    private static string PadBase64(string input)
    {
        return input.PadRight(input.Length + (4 - input.Length % 4) % 4, '=');
    }
}


using System.Text.Json;
using CeresMockServer.Data;
using CeresMockServer.Options;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace CeresMockServer.Services;

/// <summary>
/// Evaluates alerts for a given tag ESN using sliding windows over Observations and Tag state.
/// </summary>
public interface IAlertEvaluator
{
    /// <summary>
    /// Evaluate all alert conditions for the specified tag ESN using recent data.
    /// </summary>
    Task<IReadOnlyList<Alert>> EvaluateAsync(string esn, CancellationToken ct = default);
}

public class AlertEvaluator : IAlertEvaluator
{
    private readonly CeresDbContext _db;
    private readonly DataGenOptions _opts;

    public AlertEvaluator(CeresDbContext db, IOptions<DataGenOptions> opts)
    {
        _db = db;
        _opts = opts.Value;
    }

    public async Task<IReadOnlyList<Alert>> EvaluateAsync(string esn, CancellationToken ct = default)
    {
        var results = new List<Alert>();
        var tag = await _db.Tags.AsNoTracking().SingleOrDefaultAsync(t => t.Esn == esn, ct);
        if (tag == null) return results;

        var now = DateTime.UtcNow;
        var thirtyMin = now.AddMinutes(-30);
        var sixtyMin = now.AddMinutes(-60);

        var recent = await _db.Observations.AsNoTracking()
            .Where(o => o.Esn == esn && o.Timestamp >= sixtyMin && o.Timestamp <= now)
            .OrderBy(o => o.Timestamp)
            .ToListAsync(ct);

        if (recent.Count > 0)
        {
            // High activity over last 30 min
            var last30 = recent.Where(o => o.Timestamp >= thirtyMin).ToList();
            if (last30.Count > 0)
            {
                var walking = last30.Count(o => o.Activity == "walking");
                var ratio = (double)walking / last30.Count;
                if (ratio > _opts.HighActivityThreshold)
                {
                    results.Add(new Alert
                    {
                        Esn = esn,
                        AlertType = "high_activity",
                        Severity = "warning",
                        Timestamp = now,
                        Metadata = JsonSerializer.Serialize(new { ratio })
                    });
                }
            }

            // No activity over last 60 min
            var resting = recent.Count(o => o.Timestamp >= sixtyMin && o.Activity == "resting");
            var ratioRest = (double)resting / recent.Count;
            if (ratioRest > _opts.NoActivityThreshold)
            {
                results.Add(new Alert
                {
                    Esn = esn,
                    AlertType = "no_activity",
                    Severity = "warning",
                    Timestamp = now,
                    Metadata = JsonSerializer.Serialize(new { ratio = ratioRest })
                });
            }

            // Geofence breach: any point beyond radius
            foreach (var obs in recent)
            {
                var dist = HaversineMeters(obs.Latitude, obs.Longitude, _opts.DefaultCenterLat, _opts.DefaultCenterLon);
                if (dist > _opts.PaddockRadiusMeters)
                {
                    results.Add(new Alert
                    {
                        Esn = esn,
                        AlertType = "geofence_breach",
                        Severity = "critical",
                        Timestamp = obs.Timestamp,
                        Metadata = JsonSerializer.Serialize(new { distance_m = dist, lat = obs.Latitude, lon = obs.Longitude })
                    });
                    break;
                }
            }
        }

        // Low battery
        if (tag.BatteryPercentage < _opts.LowBatteryThreshold)
        {
            results.Add(new Alert
            {
                Esn = esn,
                AlertType = "low_battery",
                Severity = "warning",
                Timestamp = now,
                Metadata = JsonSerializer.Serialize(new { battery = tag.BatteryPercentage })
            });
        }

        return results;
    }

    private static double HaversineMeters(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371000.0;
        var dLat = ToRad(lat2 - lat1);
        var dLon = ToRad(lon2 - lon1);
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) + Math.Cos(ToRad(lat1)) * Math.Cos(ToRad(lat2)) * Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }
    private static double ToRad(double deg) => deg * Math.PI / 180.0;
}


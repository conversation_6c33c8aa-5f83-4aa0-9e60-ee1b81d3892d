using System.Text.Json;
using CeresTagClient.Core.Dtos.Webhooks;
using CeresTagClient.Core.Interfaces;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;

namespace CeresTagClient.Application.Webhooks;

public class WebhookProcessor : IWebhookProcessor
{
    private readonly IEnumerable<IWebhookEventHandler<AlertWebhookDto>> _alertHandlers;
    private readonly IEnumerable<IWebhookEventHandler<PfiWebhookDto>> _pfiHandlers;
    private readonly IEnumerable<IWebhookEventHandler<HistoricalWebhookDto>> _historicalHandlers;
    private readonly IEnumerable<IWebhookEventHandler<StandardWebhookDto>> _standardHandlers;

    [ActivatorUtilitiesConstructor]
    public WebhookProcessor(
        IEnumerable<IWebhookEventHandler<AlertWebhookDto>> alertHandlers,
        IEnumerable<IWebhookEventHandler<PfiWebhookDto>> pfiHandlers,
        IEnumerable<IWebhookEventHandler<HistoricalWebhookDto>> historicalHandlers,
        IEnumerable<IWebhookEventHandler<StandardWebhookDto>> standardHandlers)
    {
        _alertHandlers = alertHandlers;
        _pfiHandlers = pfiHandlers;
        _historicalHandlers = historicalHandlers;
        _standardHandlers = standardHandlers;
    }

    // Back-compat constructor for older tests that provided IHttpClientFactory
    public WebhookProcessor(IHttpClientFactory _)
        : this(Enumerable.Empty<IWebhookEventHandler<AlertWebhookDto>>(),
               Enumerable.Empty<IWebhookEventHandler<PfiWebhookDto>>(),
               Enumerable.Empty<IWebhookEventHandler<HistoricalWebhookDto>>(),
               Enumerable.Empty<IWebhookEventHandler<StandardWebhookDto>>())
    { }

    private static Task PublishAsync<T>(IEnumerable<IWebhookEventHandler<T>> handlers, T payload, CancellationToken ct)
    {
        // Fan-out to all registered handlers; fail fast if any throws to trigger retry/ dead-letter
        return Task.WhenAll(handlers.Select(h => h.HandleAsync(payload, ct)));
    }

    public Task ProcessAlertAsync(AlertWebhookDto payload, CancellationToken ct = default)
    {
        if (payload.version != "1.0") throw new InvalidOperationException("Unsupported payload version");
        return PublishAsync(_alertHandlers, payload, ct);
    }

    public Task ProcessPfiAsync(PfiWebhookDto payload, CancellationToken ct = default)
    {
        if (payload.version != "1.0") throw new InvalidOperationException("Unsupported payload version");
        return PublishAsync(_pfiHandlers, payload, ct);
    }

    public Task ProcessHistoricalAsync(HistoricalWebhookDto payload, CancellationToken ct = default)
    {
        if (payload.version != "1.0") throw new InvalidOperationException("Unsupported payload version");
        return PublishAsync(_historicalHandlers, payload, ct);
    }

    public Task ProcessStandardAsync(StandardWebhookDto payload, CancellationToken ct = default)
    {
        if (payload.version != "1.0") throw new InvalidOperationException("Unsupported payload version");
        return PublishAsync(_standardHandlers, payload, ct);
    }
}


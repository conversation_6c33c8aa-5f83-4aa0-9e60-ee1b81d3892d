using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CeresMockServer.Tests;

public class HistoricalControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public HistoricalControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder => { });
    }

    [Fact]
    public async Task RequestHistorical_Returns_200_And_RequestId()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var seedEsn = "100000000000000";
        var payload = new { startDate = DateTime.UtcNow.AddDays(-1), endDate = DateTime.UtcNow };
        var resp = await client.PostAsJsonAsync($"/api/v1/tags/{seedEsn}/historical/request", payload);
        Assert.Equal(HttpStatusCode.OK, resp.StatusCode);
        var dto = await resp.Content.ReadFromJsonAsync<HistoricalRequestResponse>();
        Assert.False(string.IsNullOrWhiteSpace(dto!.RequestId));
    }

    [Fact]
    public async Task RequestHistorical_Returns_400_For_Invalid_Range()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var seedEsn = "100000000000000";
        var payload = new { startDate = DateTime.UtcNow, endDate = DateTime.UtcNow.AddDays(-1) };
        var resp = await client.PostAsJsonAsync($"/api/v1/tags/{seedEsn}/historical/request", payload);
        Assert.Equal(HttpStatusCode.BadRequest, resp.StatusCode);
        var pd = await resp.Content.ReadFromJsonAsync<ProblemDetails>();
        Assert.Equal("validation_failed", pd!.Title);
        Assert.Equal("about:blank", pd.Type);
    }

    [Fact]
    public async Task RetrieveHistorical_Returns_200_With_Pagination()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var seedEsn = "100000000000000";
        var reqPayload = new { startDate = DateTime.UtcNow.AddHours(-3), endDate = DateTime.UtcNow };
        var reqResp = await client.PostAsJsonAsync($"/api/v1/tags/{seedEsn}/historical/request", reqPayload);
        var reqDto = await reqResp.Content.ReadFromJsonAsync<HistoricalRequestResponse>();

        var resp = await client.GetAsync($"/api/v1/tags/{seedEsn}/historical/{reqDto!.RequestId}?pageSize=100&pageNumber=1");
        Assert.Equal(HttpStatusCode.OK, resp.StatusCode);
        var dto = await resp.Content.ReadFromJsonAsync<HistoricalRetrieveResponse>();
        Assert.Equal("ready", dto!.Status);
        Assert.NotNull(dto.Pagination);
        Assert.Equal(100, dto.Pagination.PageSize);
    }

    [Fact]
    public async Task RetrieveHistorical_Returns_400_For_Invalid_Pagination()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var seedEsn = "100000000000000";
        var reqPayload = new { startDate = DateTime.UtcNow.AddHours(-3), endDate = DateTime.UtcNow };
        var reqResp = await client.PostAsJsonAsync($"/api/v1/tags/{seedEsn}/historical/request", reqPayload);
        var reqDto = await reqResp.Content.ReadFromJsonAsync<HistoricalRequestResponse>();

        var resp = await client.GetAsync($"/api/v1/tags/{seedEsn}/historical/{reqDto!.RequestId}?pageSize=0&pageNumber=0");
        Assert.Equal(HttpStatusCode.BadRequest, resp.StatusCode);
        var pd = await resp.Content.ReadFromJsonAsync<ProblemDetails>();
        Assert.Equal("validation_failed", pd!.Title);
    }

    [Fact]
    public async Task Auth_Fails_For_Invalid_Token()
    {
        var client = _factory.CreateClient();
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", "invalid.token");

        var seedEsn = "100000000000000";
        var payload = new { startDate = DateTime.UtcNow.AddDays(-1), endDate = DateTime.UtcNow };
        var resp = await client.PostAsJsonAsync($"/api/v1/tags/{seedEsn}/historical/request", payload);
        Assert.Equal(HttpStatusCode.Unauthorized, resp.StatusCode);
    }
}

public class HistoricalControllerEdgeTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    public HistoricalControllerEdgeTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder => { });
    }

    [Fact]
    public async Task RequestHistorical_Returns_404_For_Tag_Not_Found()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var missingEsn = "999999999999999"; // not seeded
        var payload = new { startDate = DateTime.UtcNow.AddDays(-1), endDate = DateTime.UtcNow };
        var resp = await client.PostAsJsonAsync($"/api/v1/tags/{missingEsn}/historical/request", payload);
        Assert.Equal(HttpStatusCode.NotFound, resp.StatusCode);
        var pd = await resp.Content.ReadFromJsonAsync<ProblemDetails>();
        Assert.Equal("tag_not_found", pd!.Title);
        Assert.Equal(404, pd.Status);
        Assert.Equal("about:blank", pd.Type);
        Assert.Contains(missingEsn, pd.Detail);
    }

    [Fact]
    public async Task RequestHistorical_Returns_400_When_Range_Exceeds_31_Days()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var seedEsn = "100000000000000";
        var payload = new { startDate = DateTime.UtcNow.AddDays(-40), endDate = DateTime.UtcNow };
        var resp = await client.PostAsJsonAsync($"/api/v1/tags/{seedEsn}/historical/request", payload);
        Assert.Equal(HttpStatusCode.BadRequest, resp.StatusCode);
        var pd = await resp.Content.ReadFromJsonAsync<ProblemDetails>();
        Assert.Equal("validation_failed", pd!.Title);
        Assert.Equal(400, pd.Status);
        Assert.Contains("31 days", pd.Detail!, StringComparison.OrdinalIgnoreCase);
    }

    [Fact]
    public async Task RetrieveHistorical_Returns_400_For_Malformed_RequestId()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var seedEsn = "100000000000000";
        var malformed = "not-base64";
        var resp = await client.GetAsync($"/api/v1/tags/{seedEsn}/historical/{malformed}?pageSize=100&pageNumber=1");
        Assert.Equal(HttpStatusCode.BadRequest, resp.StatusCode);
        var pd = await resp.Content.ReadFromJsonAsync<ProblemDetails>();
        Assert.Equal("validation_failed", pd!.Title);
        Assert.Equal(400, pd.Status);
        Assert.Equal("about:blank", pd.Type);
        Assert.Contains("requestId", pd.Detail!, StringComparison.OrdinalIgnoreCase);
    }

    [Fact]
    public async Task RetrieveHistorical_Returns_400_For_RequestId_With_Wrong_Esn()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var seedEsn = "100000000000000";
        var otherEsn = "100000000000001";
        var start = DateTime.UtcNow.AddHours(-2).ToString("O");
        var end = DateTime.UtcNow.ToString("O");
        var tokenStr = $"{otherEsn}|{start}|{end}";
        var badReqId = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(tokenStr)).TrimEnd('=');

        var resp = await client.GetAsync($"/api/v1/tags/{seedEsn}/historical/{badReqId}?pageSize=100&pageNumber=1");
        Assert.Equal(HttpStatusCode.BadRequest, resp.StatusCode);
        var pd = await resp.Content.ReadFromJsonAsync<ProblemDetails>();
        Assert.Equal("validation_failed", pd!.Title);
        Assert.Equal(400, pd.Status);
        Assert.Equal("about:blank", pd.Type);
        Assert.Contains("requestId", pd.Detail!, StringComparison.OrdinalIgnoreCase);
    }

    [Fact]
    public async Task RetrieveHistorical_Returns_404_For_Tag_Not_Found()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var missingEsn = "999999999999999";
        var start = DateTime.UtcNow.AddHours(-2).ToString("O");
        var end = DateTime.UtcNow.ToString("O");
        var tokenStr = $"{missingEsn}|{start}|{end}";
        var reqId = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(tokenStr)).TrimEnd('=');

        var resp = await client.GetAsync($"/api/v1/tags/{missingEsn}/historical/{reqId}?pageSize=100&pageNumber=1");
        Assert.Equal(HttpStatusCode.NotFound, resp.StatusCode);
        var pd = await resp.Content.ReadFromJsonAsync<ProblemDetails>();
        Assert.Equal("tag_not_found", pd!.Title);
        Assert.Equal(404, pd.Status);
        Assert.Equal("about:blank", pd.Type);
        Assert.Contains(missingEsn, pd.Detail!);
    }
}



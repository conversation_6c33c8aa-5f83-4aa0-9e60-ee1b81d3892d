using System.Net.Http.Headers;
using System.Text;
using CeresMockServer.Options;
using Microsoft.Extensions.Options;

namespace CeresMockServer.Services;

public class WebhookDispatcher : BackgroundService
{
    private readonly IWebhookQueue _queue;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IWebhookLocalSink _localSink;
    private readonly ILogger<WebhookDispatcher> _logger;
    private readonly WebhookOptions _options;
    private readonly WebhookFailureRuntime _runtime;
    private readonly IWebhookDeadLetterStore _deadletters;

    public WebhookDispatcher(IWebhookQueue queue, IHttpClientFactory httpClientFactory, IWebhookLocalSink localSink, IOptions<WebhookOptions> options, WebhookFailureRuntime runtime, IWebhookDeadLetterStore deadletters, ILogger<WebhookDispatcher> logger)
    {
        _queue = queue;
        _httpClientFactory = httpClientFactory;
        _localSink = localSink;
        _logger = logger;
        _options = options.Value;
        _runtime = runtime;
        _deadletters = deadletters;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var client = _httpClientFactory.CreateClient("webhook");
        while (!stoppingToken.IsCancellationRequested)
        {
            if (_queue.TryDequeue(out var message) && message is not null)
            {
                try
                {
                    await Task.Delay(_options.DeliveryDelayMs, stoppingToken);
                    var signature = WebhookSigning.ComputeSignatureHex(message.SharedSecret, message.Payload);
                    var headers = new Dictionary<string, string>
                    {
                        ["X-Ceres-Signature"] = $"sha256={signature}",
                        ["X-Ceres-Timestamp"] = ((DateTimeOffset)message.TimestampUtc).ToUnixTimeSeconds().ToString(),
                        ["Content-Type"] = "application/json; charset=utf-8"
                    };

                    // Support relative URLs in tests by routing to local sink
                    if (Uri.TryCreate(message.Url, UriKind.RelativeOrAbsolute, out var uri) && !uri.IsAbsoluteUri)
                    {
                        // apply failure policy
                        if (_runtime.Value.ForceFailure || ShouldRandomFail())
                        {
                            AddDeadLetter(message, 1, "Forced or random failure", headers);
                        }
                        else
                        {
                            await _localSink.DeliverAsync(message.Url, headers, message.Payload, stoppingToken);
                        }
                    }
                    else
                    {
                        var attempt = 0;
                        while (true)
                        {
                            attempt++;
                            try
                            {
                                var content = new StringContent(message.Payload, Encoding.UTF8, "application/json");
                                using var req = new HttpRequestMessage(HttpMethod.Post, message.Url);
                                req.Content = content;
                                foreach (var h in headers)
                                    req.Headers.TryAddWithoutValidation(h.Key, h.Value);

                                if (_runtime.Value.ForceFailure || ShouldRandomFail())
                                {
                                    if (attempt >= _options.MaxRetries)
                                    {
                                        AddDeadLetter(message, attempt, "Forced or random failure", headers);
                                        break;
                                    }
                                }
                                else
                                {
                                    var resp = await client.SendAsync(req, stoppingToken);
                                    if (resp.IsSuccessStatusCode) break;
                                    if (attempt >= _options.MaxRetries)
                                    {
                                        AddDeadLetter(message, attempt, $"HTTP {(int)resp.StatusCode}", headers);
                                        break;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                if (attempt >= _options.MaxRetries)
                                {
                                    AddDeadLetter(message, attempt, ex.Message, headers);
                                    break;
                                }
                            }
                            await BackoffDelayAsync(attempt, stoppingToken);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to deliver webhook message");
                }
            }
            else
            {
                await Task.Delay(25, stoppingToken);
            }
        }
    }
    private static int Pow2(int n) => 1 << Math.Clamp(n, 0, 30);
    private async Task BackoffDelayAsync(int attempt, CancellationToken ct)
    {
        var baseMs = Math.Max(1, _options.RetryBackoffMs);
        var factor = Pow2(attempt - 1);
        var jitter = Random.Shared.Next(0, baseMs);
        var delay = Math.Min(30_000, baseMs * factor + jitter);
        await Task.Delay(delay, ct);
    }

    private bool ShouldRandomFail()
    {
        var pct = Math.Clamp(_runtime.Value.RandomFailurePercent, 0, 100);
        return pct > 0 && Random.Shared.Next(0, 100) < pct;
    }

    private void AddDeadLetter(WebhookMessage msg, int attempts, string reason, IDictionary<string, string> headers)
    {
        _deadletters.Add(new WebhookDeadLetter
        {
            Url = msg.Url,
            Type = msg.Type,
            Attempts = attempts,
            Reason = reason,
            Headers = headers.ToDictionary(k => k.Key, v => v.Value),
            Payload = msg.Payload,
            TimestampUtc = DateTime.UtcNow
        });
    }
}


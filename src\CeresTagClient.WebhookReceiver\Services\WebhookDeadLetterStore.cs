using System.Collections.Concurrent;

namespace CeresTagClient.WebhookReceiver.Services;

public class ClientDeadLetter
{
    public DateTime TimestampUtc { get; init; } = DateTime.UtcNow;
    public string Type { get; init; } = string.Empty;
    public int Attempts { get; init; }
    public string Reason { get; init; } = string.Empty;
    public string Payload { get; init; } = string.Empty;
}

public interface IWebhookDeadLetterStore
{
    void Add(ClientDeadLetter entry);
    IReadOnlyList<ClientDeadLetter> GetAll();
    void Clear();
}

public class InMemoryWebhookDeadLetterStore : IWebhookDeadLetterStore
{
    private readonly ConcurrentQueue<ClientDeadLetter> _queue = new();

    public void Add(ClientDeadLetter entry) => _queue.Enqueue(entry);

    public IReadOnlyList<ClientDeadLetter> GetAll() => _queue.ToArray();

    public void Clear()
    {
        while (_queue.TryDequeue(out _)) { }
    }
}


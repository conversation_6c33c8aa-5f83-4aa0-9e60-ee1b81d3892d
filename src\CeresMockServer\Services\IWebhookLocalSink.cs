using CeresMockServer.Controllers;

namespace CeresMockServer.Services;

public interface IWebhookLocalSink
{
    Task DeliverAsync(string path, Dictionary<string, string> headers, string body, CancellationToken ct);
}

public class TestEndpointLocalSink : IWebhookLocalSink
{
    public Task DeliverAsync(string path, Dictionary<string, string> headers, string body, CancellationToken ct)
    {
        // Only supports the test receiver path in tests
        if (path.StartsWith("/api/v1/test-webhook"))
        {
            TestWebhookReceiverController.AddFromSink(path, headers, body);
        }
        return Task.CompletedTask;
    }
}


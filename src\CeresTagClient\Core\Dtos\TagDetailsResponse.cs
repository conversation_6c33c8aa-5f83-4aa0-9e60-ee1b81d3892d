using System.Text.Json.Serialization;

namespace CeresTagClient.Core.Dtos;

public class TagDetailsResponse
{
    [JsonPropertyName("esn")] public string Esn { get; set; } = string.Empty;
    [JsonPropertyName("brand")] public string Brand { get; set; } = string.Empty;
    [JsonPropertyName("firmwareVersion")] public string FirmwareVersion { get; set; } = string.Empty;
    [JsonPropertyName("activationDate")] public DateTime ActivationDate { get; set; }
    [JsonPropertyName("batteryPercentage")] public int BatteryPercentage { get; set; }
    [JsonPropertyName("status")] public string Status { get; set; } = string.Empty;
    [JsonPropertyName("property")] public PropertyDto Property { get; set; } = new();
    [JsonPropertyName("lastSeen")] public DateTime? LastSeen { get; set; }
}

public class PropertyDto
{
    [JsonPropertyName("id")] public string Id { get; set; } = string.Empty;
    [JsonPropertyName("name")] public string Name { get; set; } = string.Empty;
}


using CeresTagClient.Application.Sync;
using CeresTagClient.Core.Options;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;

namespace CeresTagClient.WebhookReceiver.Services;

public class SyncBackgroundService(
    IServiceProvider services,
    IOptions<SyncOptions> options,
    ILogger<SyncBackgroundService> logger) : BackgroundService
{
    private readonly SyncOptions _opts = options.Value;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var delay = TimeSpan.FromSeconds(Math.Max(1, _opts.IntervalSeconds));
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                if (_opts.Enabled)
                {
                    // Create a scope per execution to resolve scoped orchestrator dependencies safely
                    using var scope = services.CreateScope();
                    var orchestrator = scope.ServiceProvider.GetRequiredService<ISyncOrchestrator>();
                    var count = await orchestrator.RunOnceAsync(stoppingToken);
                    logger.LogInformation("SyncBackgroundService executed. Items processed: {Count}", count);
                }
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error during sync execution");
            }

            try
            {
                await Task.Delay(delay, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
        }
    }
}


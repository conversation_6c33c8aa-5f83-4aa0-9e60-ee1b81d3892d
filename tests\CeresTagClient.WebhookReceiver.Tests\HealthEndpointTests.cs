using System.Text.Json.Nodes;
using System.Net.Http.Json;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Xunit;

namespace CeresTagClient.WebhookReceiver.Tests;

public class HealthEndpointTests : IClassFixture<WebApplicationFactory<CeresTagClient.WebhookReceiver.Program>>
{
    private readonly WebApplicationFactory<CeresTagClient.WebhookReceiver.Program> _factory;

    public HealthEndpointTests(WebApplicationFactory<CeresTagClient.WebhookReceiver.Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, cfg) =>
            {
                // Minimal configuration to allow startup
                cfg.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["WebhookReceiver:SharedSecret"] = "secretsecret",
                    ["CeresApi:BaseUrl"] = "http://localhost",
                    ["CeresApi:TimeoutSeconds"] = "5",
                    ["Sync:Enabled"] = "false"
                });
            });
        });
    }

    [Fact]
    public async Task Health_Endpoint_Returns_Ok()
    {
        var client = _factory.CreateClient(new WebApplicationFactoryClientOptions { AllowAutoRedirect = false });
        var resp = await client.GetAsync("/health");
        resp.EnsureSuccessStatusCode();

        // Validate payload shape
        var json = await resp.Content.ReadFromJsonAsync<JsonObject>();
        Assert.NotNull(json);
        Assert.Equal("ok", json!["status"]!.GetValue<string>());
    }
}


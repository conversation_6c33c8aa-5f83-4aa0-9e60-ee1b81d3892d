using System.Security.Cryptography;
using System.Text;
using CeresTagClient.Core.Interfaces;

namespace CeresTagClient.Application.Webhooks;

public class HmacSha256SignatureValidator : IWebhookSignatureValidator
{
    public bool IsValid(string payload, string? signatureHeader, string sharedSecret)
    {
        if (string.IsNullOrWhiteSpace(signatureHeader) || string.IsNullOrWhiteSpace(sharedSecret)) return false;
        // Expected format: "sha256=<hex>"
        var parts = signatureHeader.Split('=', 2);
        if (parts.Length != 2) return false;
        if (!parts[0].Equals("sha256", StringComparison.OrdinalIgnoreCase)) return false;
        var providedHex = parts[1].Trim();

        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(sharedSecret));
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(payload));
        var expectedHex = Convert.ToHexString(hash).ToLowerInvariant();

        // Constant-time compare
        return FixedTimeEquals(providedHex, expectedHex);
    }

    private static bool FixedTimeEquals(string a, string b)
    {
        if (a.Length != b.Length) return false;
        var result = 0;
        for (int i = 0; i < a.Length; i++)
        {
            result |= a[i] ^ b[i];
        }
        return result == 0;
    }
}


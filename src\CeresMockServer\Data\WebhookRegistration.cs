namespace CeresMockServer.Data;

public class WebhookRegistration
{
    public long Id { get; set; }
    public string Url { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // alert, pfi, historical, standard
    public string SharedSecret { get; set; } = string.Empty;
    public bool Active { get; set; } = true;
    public string? Metadata { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}


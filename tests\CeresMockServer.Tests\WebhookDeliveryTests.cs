using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using CeresMockServer;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CeresMockServer.Tests;

public class WebhookDeliveryTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public WebhookDeliveryTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["ConnectionStrings:CeresDb"] = $"Data Source=webhook-delivery-{Guid.NewGuid():N}.db"
                };
                config.AddInMemoryCollection(dict);
            });
        });
    }

    [Fact]
    public async Task Simulate_Alert_Delivers_To_TestReceiver_With_Signature()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        // Register webhook pointing to test receiver
        var uniquePath = $"/api/v1/test-webhook/alerts-{Guid.NewGuid():N}";
        var hook = new WebhookCreateRequest
        {
            Url = uniquePath,
            Type = "alert",
            SharedSecret = "secretsecret",
            Active = true
        };
        var createResp = await client.PostAsJsonAsync("/api/v1/webhooks", hook);
        Assert.Equal(HttpStatusCode.Created, createResp.StatusCode);

        // clear any previous receipts for this path
        await client.DeleteAsync($"/api/v1/test-webhook?path={Uri.EscapeDataString(uniquePath)}");

        // Trigger simulator
        var simResp = await client.PostAsync("/api/v1/webhooks/simulate/alert", null);
        Assert.Equal(HttpStatusCode.Accepted, simResp.StatusCode);

        // Poll test receiver memory until it gets one
        for (int i = 0; i < 20; i++)
        {
            var get = await client.GetAsync($"/api/v1/test-webhook/inspect?path={Uri.EscapeDataString(uniquePath)}");
            var items = await get.Content.ReadFromJsonAsync<List<TestWebhookItem>>();
            if (items!.Count > 0)
            {
                var item = items![^1];
                Assert.NotNull(item.Headers["X-Ceres-Signature"]);
                Assert.StartsWith("sha256=", item.Headers["X-Ceres-Signature"]!.ToString());
                Assert.NotNull(item.Headers["X-Ceres-Timestamp"]);

                // Verify signature
                var expected = CeresMockServer.Services.WebhookSigning.ComputeSignatureHex(hook.SharedSecret, item.Body);
                Assert.Equal($"sha256={expected}", item.Headers["X-Ceres-Signature"]!.ToString());
                return;
            }
            await Task.Delay(50);
        }

        Assert.True(false, "No webhook received by test endpoint in time");
    }

    private record TestWebhookItem(Dictionary<string, string> Headers, string Body);
}


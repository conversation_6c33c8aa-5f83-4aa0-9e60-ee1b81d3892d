using System.Net.Http.Headers;
using System.Net.Http.Json;
using CeresMockServer;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CeresMockServer.Tests;

public class WebhookFailureAndBackoffTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public WebhookFailureAndBackoffTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["ConnectionStrings:CeresDb"] = $"Data Source=webhook-failure-{Guid.NewGuid():N}.db",
                    ["CeresMock:Webhooks:DeliveryDelayMs"] = "1",
                    ["CeresMock:Webhooks:MaxRetries"] = "3",
                    ["CeresMock:Webhooks:RetryBackoffMs"] = "10",
                    ["CeresMock:WebhookFailures:ForceFailure"] = "true"
                };
                config.AddInMemoryCollection(dict);
            });
        });
    }

    [Fact]
    public async Task Delivery_Retries_With_Exponential_Backoff_When_Forced_Failure()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var uniquePath = $"/api/v1/test-webhook/failure-{Guid.NewGuid():N}";
        var hook = new WebhookCreateRequest
        {
            Url = uniquePath,
            Type = "standard",
            SharedSecret = "foobarbaz",
            Active = true
        };
        var createResp = await client.PostAsJsonAsync("/api/v1/webhooks", hook);
        createResp.EnsureSuccessStatusCode();

        // Clear any prior receipts for this path
        await client.DeleteAsync($"/api/v1/test-webhook?path={Uri.EscapeDataString(uniquePath)}");

        var t0 = DateTime.UtcNow;
        var simResp = await client.PostAsync("/api/v1/webhooks/simulate/standard", null);
        simResp.EnsureSuccessStatusCode();

        // Wait a bounded time to allow dispatcher to attempt and backoff; since it always fails, no receipt should appear
        await Task.Delay(500);

        var inspect = await client.GetAsync($"/api/v1/test-webhook/inspect?path={Uri.EscapeDataString(uniquePath)}");
        var items = await inspect.Content.ReadFromJsonAsync<List<object>>();
        Assert.Empty(items!);

        // Sanity check backoff time grew beyond base backoff
        var elapsedMs = (DateTime.UtcNow - t0).TotalMilliseconds;
        Assert.True(elapsedMs >= 10, $"elapsed {elapsedMs}ms should be >= base backoff");
    }
}


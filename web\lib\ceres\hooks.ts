import { useQuery, useQueries, UseQueryResult } from '@tanstack/react-query';
import { fetchTagDetails, fetchRecent } from './client';
import type { RecentDataResponse } from './types';

export function useTagDetails(esn: string) {
  return useQuery({
    queryKey: ['tag', esn],
    queryFn: () => fetchTagDetails(esn),
    refetchInterval: 30_000,
  });
}

export function useTagRecent(esn: string, fromDate?: string) {
  return useQuery({
    queryKey: ['recent', esn, fromDate ?? ''],
    queryFn: () => fetchRecent(esn, fromDate),
    refetchInterval: 5_000,
  });
}

export function useRecentMany(esns: string[], fromDate?: string) {
  const results = useQueries({
    queries: esns.map((esn) => ({
      queryKey: ['recent', esn, fromDate ?? ''],
      queryFn: () => fetchRecent(esn, fromDate),
      refetchInterval: 5_000,
    })),
  }) as UseQueryResult<RecentDataResponse, Error>[];

  return results;
}


using System.Net.Http.Json;
using CeresTagClient.Core.Dtos;
using CeresTagClient.Core.Interfaces;

namespace CeresTagClient.Application.Services;

public class PropertyService(IHttpClientFactory factory) : IPropertyService
{
    private readonly HttpClient _http = factory.CreateClient("CeresApi");

    public async Task<IReadOnlyList<CustomerLinkResponse>> GetPropertyLinksAsync(string customerId, CancellationToken ct = default)
    {
        var resp = await _http.GetAsync($"api/v1/customer-linkage/links/{customerId}", ct);
        resp.EnsureSuccessStatusCode();
        var payload = await resp.Content.ReadFromJsonAsync<List<CustomerLinkResponse>>(cancellationToken: ct);
        return payload ?? new List<CustomerLinkResponse>();
    }
}


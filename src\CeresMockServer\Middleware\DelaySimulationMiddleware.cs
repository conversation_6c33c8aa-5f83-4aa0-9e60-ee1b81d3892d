using Microsoft.Extensions.Options;

namespace CeresMockServer.Middleware;

public class DelaySimulationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<DelaySimulationMiddleware> _logger;
    private readonly CeresMockServer.Options.ResponseSimulationOptions _options;

    public DelaySimulationMiddleware(RequestDelegate next, IOptions<CeresMockServer.Options.ResponseSimulationOptions> options, ILogger<DelaySimulationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
        _options = options.Value;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Bypass if header present
        if (!string.IsNullOrWhiteSpace(_options.BypassHeaderName) &&
            context.Request.Headers.TryGetValue(_options.BypassHeaderName, out var bypass) &&
            string.Equals(bypass.ToString(), "true", StringComparison.OrdinalIgnoreCase))
        {
            context.Response.Headers["X-Delay-Applied"] = "false";
            await _next(context);
            return;
        }

        var baseDelay = Math.Max(0, _options.DefaultDelayMs);
        var jitterMax = Math.Max(0, _options.RandomDelayMax);
        var jitter = jitterMax > 0 ? Random.Shared.Next(0, jitterMax + 1) : 0;
        var totalDelay = baseDelay + jitter;

        if (totalDelay > 0)
        {
            try
            {
                await Task.Delay(totalDelay, context.RequestAborted);
                context.Response.Headers["X-Delay-Applied"] = "true";
                context.Response.Headers["X-Delay-TotalMs"] = totalDelay.ToString();
            }
            catch (OperationCanceledException)
            {
                // Aborted
            }
        }
        else
        {
            context.Response.Headers["X-Delay-Applied"] = "false";
        }

        await _next(context);
    }
}


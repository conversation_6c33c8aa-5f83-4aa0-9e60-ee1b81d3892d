# CeresTagClient.WebhookReceiver

## Sync background service

The receiver can optionally run a background synchronization loop that periodically fetches recent tag data and persists it using the client library. This is implemented as:

- SyncBackgroundService (hosted service in this project)
- ISyncOrchestrator (from the client library) which executes a single sync pass

The hosted service simply calls orchestrator.RunOnceAsync() at a configured interval when Sync.Enabled is true. All configuration lives under the `Sync` section of appsettings.

### Configuration (appsettings.json)

Sync:
- Enabled: boolean (default false). When false, the service does nothing.
- IntervalSeconds: integer (default 300). Delay between sync runs.
- PageSize: integer (default 100). Page size for API requests.
- MaxPagesPerRun: integer (default 10). Hard limit of pages per ESN per run.
- BatchSize: integer (default 200). Persistence batch size.
- Esns: array of strings. ESNs to synchronize.
- InitialLookbackHours: integer (default 12). Lookback when no prior sync exists.

Example (enable sync):
{
  "Sync": {
    "Enabled": true,
    "IntervalSeconds": 60,
    "PageSize": 200,
    "MaxPagesPerRun": 5,
    "BatchSize": 500,
    "InitialLookbackHours": 6,
    "Esns": [
      "100000000000000",
      "200000000000000"
    ]
  }
}

Example (disable sync):
{
  "Sync": {
    "Enabled": false
  }
}

### How it integrates

- Program.cs calls AddClientSyncHosted(Configuration), which:
  - Binds `Sync` options
  - Registers SyncBackgroundService as a hosted service
- The client library registers ISyncOrchestrator (and in-memory repositories by default). The hosted service obtains ISyncOrchestrator from DI and executes it according to configuration.

No additional setup is required. To change runtime behavior, update the `Sync` configuration.


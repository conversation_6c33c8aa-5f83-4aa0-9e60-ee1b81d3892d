using CeresTagClient.Application.Sync;
using CeresTagClient.Core.Options;

namespace CeresTagClient.WebhookReceiver.Services;

public static class SyncHostedRegistration
{
    public static IServiceCollection AddClientSyncHosted(this IServiceCollection services, IConfiguration config)
    {
        services.Configure<SyncOptions>(config.GetSection("Sync"));
        services.AddHostedService<SyncBackgroundService>();
        return services;
    }
}


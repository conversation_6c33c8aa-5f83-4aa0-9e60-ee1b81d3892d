using System.Security.Cryptography;
using System.Text;

namespace CeresMockServer.Services;

public static class WebhookSigning
{
    public static string ComputeSignatureHex(string sharedSecret, string payload)
    {
        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(sharedSecret));
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(payload));
        return Convert.ToHexString(hash).ToLowerInvariant();
    }
}


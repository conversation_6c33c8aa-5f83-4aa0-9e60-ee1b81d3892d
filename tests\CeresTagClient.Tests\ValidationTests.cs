using CeresTagClient.Application.Validation;
using CeresTagClient.Core.Requests;

namespace CeresTagClient.Tests;

public class ValidationTests
{
    [Theory]
    [InlineData("123456789012345", true)]
    [InlineData("12345678901234", false)]
    [InlineData("1234567890123456", false)]
    [InlineData("ABCDEFGHIJKLMNO", false)]
    public void EsnValidation_Works(string esn, bool expected)
    {
        var v1 = new TagDetailsRequestValidator();
        var v2 = new RecentTagDataRequestValidator();

        var r1 = v1.Validate(new TagDetailsRequest { ESN = esn });
        var r2 = v2.Validate(new RecentTagDataRequest { ESN = esn });

        Assert.Equal(expected, r1.IsValid);
        Assert.Equal(expected, r2.IsValid);
    }

    [Theory]
    [InlineData(100, 1, true)]
    [InlineData(1, 1, true)]
    [InlineData(1000, 1, true)]
    [InlineData(1001, 1, false)]
    [InlineData(0, 1, false)]
    [InlineData(100, 0, false)]
    public void PaginationValidation_Works(int pageSize, int pageNumber, bool expected)
    {
        var v = new RecentTagDataRequestValidator();
        var r = v.Validate(new RecentTagDataRequest { ESN = "123456789012345", PageSize = pageSize, PageNumber = pageNumber });
        Assert.Equal(expected, r.IsValid);
    }
}


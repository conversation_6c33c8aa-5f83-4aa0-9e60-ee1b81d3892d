using CeresTagClient.Core.Dtos.Webhooks;

namespace CeresTagClient.Core.Interfaces;

public interface IWebhookProcessor
{
    Task ProcessAlertAsync(AlertWebhookDto payload, CancellationToken ct = default);
    Task ProcessPfiAsync(PfiWebhookDto payload, CancellationToken ct = default);
    Task ProcessHistoricalAsync(HistoricalWebhookDto payload, CancellationToken ct = default);
    Task ProcessStandardAsync(StandardWebhookDto payload, CancellationToken ct = default);
}


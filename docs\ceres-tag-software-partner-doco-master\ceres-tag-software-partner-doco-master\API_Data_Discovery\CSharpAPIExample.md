# Example API Code in C#.



## Overview

C # example Code to authenticate against the test API endpoint

## Sample Code

```C#
// Author: <PERSON> (SatanEnglish)
// Date: 4th November 2020
// Description: C # example Code to authenticate against the test API endpoint

public async Task<string> CallAuthEndpoint()
    {
        var tokenInfo = GetToken(clientID, secret);

        // Now that we have the tokenInfo we can call a Ceres Portal endpoint
        var externalAPIClient = new HttpClient();
        externalAPIClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {tokenInfo.access_token}");
        var res = await externalAPIClient.GetAsync("https://extapi.test.cerestag.com/FirstContact/CheckAuthentication"); //example endpoint only

        string result = null;
        if (res.IsSuccessStatusCode)
        {
            result = await res.Content.ReadAsStringAsync();
        }
        return result;
    }

//Generate Bearer token using clientID & secret
private TokenObject GetToken(string clientID, string secret)
    {

        string BaseAuthEndPoint = "https://testcerestag.auth.ap-southeast-2.amazoncognito.com/"; //test endpoint only
        string TokenEndPoint = BaseAuthEndPoint + "oauth2/token";

        var basicPW = GenerateBase64Token(clientID, secret); //base64 encode clientId & secret
        var scope = HttpUtility.UrlEncode("https://www.cerestag.com/ctms/tags.read");

        var client = new RestClient(TokenEndPoint);
        var request = new RestRequest(Method.POST);
        request.AddHeader("cache-control", "no-cache");
        request.AddHeader("content-type", "application/x-www-form-urlencoded");
        request.AddHeader("authorization", $"Basic {basicPW}");
        request.AddParameter("application/x-www-form-urlencoded", $"grant_type=client_credentials&scope={scope}", ParameterType.RequestBody);
        var response = client.Execute(request);

        var tokenInfo = JsonConvert.DeserializeObject<TokenObject>(response.Content);
        return tokenInfo;
    }

private string GenerateBase64Token(string clientId, string clientSecret)
    { 
        var plainTextBytes = System.Text.Encoding.UTF8.GetBytes($"{clientId}:{clientSecret}");
        return System.Convert.ToBase64String(plainTextBytes);
    }

//structure of tokenObject
public class TokenObject
    {
        public string access_token { get; set; } //this will contain the Bearer token
        public string token_type { get; set; } //token type - Bearer
        public int expires_in { get; set; } // timeout of token
    }

```

using CeresMockServer.Models;
using CeresMockServer.Services;
using Microsoft.AspNetCore.Mvc;

namespace CeresMockServer.Controllers;

[ApiController]
[Route("oauth2")] // /oauth2/token
public class TokenController(IAuthenticationService authService) : ControllerBase
{
    [HttpPost("token")]
    [Consumes("application/json", "application/x-www-form-urlencoded")]
    public async Task<IActionResult> Token([FromBody] TokenRequest request)
    {
        if (!string.Equals(request.GrantType, "client_credentials", StringComparison.OrdinalIgnoreCase))
        {
            return BadRequest(new { error = "unsupported_grant_type" });
        }

        var (success, accessToken, expiresIn) = await authService.TryGetTokenAsync(request.ClientId, request.ClientSecret, scope: "read write");
        if (!success)
        {
            return Unauthorized(new { error = "invalid_client" });
        }

        return Ok(new
        {
            access_token = accessToken,
            token_type = "Bearer",
            expires_in = expiresIn
        });
    }
}


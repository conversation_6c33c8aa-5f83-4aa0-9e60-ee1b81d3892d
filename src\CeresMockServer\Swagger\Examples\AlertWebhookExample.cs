using System.Text.Json;
using CeresMockServer.Services;

namespace CeresMockServer.Swagger.Examples;

public static class AlertWebhookExample
{
    public static string Sample => JsonSerializer.Serialize(new
    {
        type = "alert",
        version = "1.0",
        alert_type = "high_activity",
        esn = "100000000000000",
        timestamp = DateTime.UtcNow,
        severity = "warning",
        location = new { lat = -27.0, lon = 151.0 },
        battery_level = 75,
        activity_summary = new { window_minutes = 30, ratio = 0.82 }
    });
}


using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using CeresMockServer.Data;
using CeresMockServer.Models;
using CeresMockServer.Options;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;

namespace CeresMockServer.Controllers;

[ApiController]
[Route("api/v1/customer-linkage")] // core endpoints for Epic 5
public class CustomerLinkageController(CeresDbContext db, IOptions<CustomerAuthOptions> options) : ControllerBase
{
    private readonly CustomerAuthOptions _opts = options.Value;

    // 1) Customer authentication redirect endpoint (start auth)
    [HttpPost("start")]
    [Authorize]
    [ProducesResponseType(typeof(CustomerAuthStartResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public IActionResult StartAuth([FromBody] CustomerAuthStartRequest req)
    {
        var state = string.IsNullOrWhiteSpace(req.State) ? Guid.NewGuid().ToString("N") : req.State;
        var redirect = string.IsNullOrWhiteSpace(req.RedirectUri) ? _opts.DefaultRedirectUri : req.RedirectUri!;
        // For the mock: generate a simple one-time code that encodes a mock customer id
        var customerId = $"cust-{Guid.NewGuid():N}";
        var code = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{customerId}|{state}"));
        var authorizationUrl = $"{redirect}?code={Uri.EscapeDataString(code)}&state={Uri.EscapeDataString(state)}";
        return Ok(new CustomerAuthStartResponse { AuthorizationUrl = authorizationUrl, State = state });
    }

    // 2) OAuth2 authorization code callback handling -> exchange code for customer token
    [HttpPost("callback")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(CustomerTokenResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Callback([FromBody] CustomerAuthCallbackRequest req)
    {
        // Decode the code to retrieve customerId and state
        string decoded;
        try
        {
            decoded = Encoding.UTF8.GetString(Convert.FromBase64String(req.Code));
        }
        catch
        {
            return Problem(title: "validation_failed", detail: "invalid_code", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
        }

        var parts = decoded.Split('|');
        if (parts.Length != 2)
            return Problem(title: "validation_failed", detail: "invalid_code", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");

        var customerId = parts[0];
        // state check is left to the client; we simply echo token creation here

        // Ensure a Customer exists
        var customer = await db.Set<Customer>().FindAsync(customerId);
        if (customer is null)
        {
            customer = new Customer { Id = customerId, DisplayName = $"Customer {customerId[..8]}" };
            await db.AddAsync(customer);
            await db.SaveChangesAsync();
        }

        // 4) Generate a customer JWT
        var expiresIn = _opts.TokenExpirySeconds;
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_opts.SigningKey));
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
        var claims = new List<Claim>
        {
            new(JwtRegisteredClaimNames.Sub, customerId),
            new("scope", "customer"),
            new("customer_id", customerId)
        };
        var jwt = new JwtSecurityToken(_opts.Issuer, _opts.Audience, claims, expires: DateTime.UtcNow.AddSeconds(expiresIn), signingCredentials: creds);
        var token = new JwtSecurityTokenHandler().WriteToken(jwt);

        return Ok(new CustomerTokenResponse { AccessToken = token, ExpiresIn = expiresIn, CustomerId = customerId });
    }

    // 5) Property mapping system (CRUD minimal)
    [HttpPost("links")]
    [Authorize]
    [ProducesResponseType(typeof(CustomerLinkResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> LinkProperty([FromBody] LinkPropertyRequest req)
    {
        if (string.IsNullOrWhiteSpace(req.CustomerId) || string.IsNullOrWhiteSpace(req.PropertyId))
            return Problem(title: "validation_failed", detail: "customerId and propertyId are required", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");

        var link = new CustomerPropertyLink
        {
            CustomerId = req.CustomerId,
            PropertyId = req.PropertyId,
            PropertyName = req.PropertyName ?? string.Empty
        };
        db.Add(link);
        await db.SaveChangesAsync();

        return CreatedAtAction(nameof(GetLinks), new { customerId = req.CustomerId }, new CustomerLinkResponse
        {
            Id = link.Id,
            CustomerId = link.CustomerId,
            PropertyId = link.PropertyId,
            PropertyName = link.PropertyName,
            LinkedAt = link.LinkedAt
        });
    }

    [HttpGet("links/{customerId}")]
    [Authorize]
    [ProducesResponseType(typeof(List<CustomerLinkResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetLinks([FromRoute] string customerId)
    {
        var links = await db.Set<CustomerPropertyLink>().Where(x => x.CustomerId == customerId).OrderBy(x => x.PropertyId)
            .Select(x => new CustomerLinkResponse
            {
                Id = x.Id,
                CustomerId = x.CustomerId,
                PropertyId = x.PropertyId,
                PropertyName = x.PropertyName,
                LinkedAt = x.LinkedAt
            }).ToListAsync();
        return Ok(links);
    }

    // 6) De-linking functionality
    [HttpDelete("links/{id:long}")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Unlink([FromRoute] long id)
    {
        var link = await db.Set<CustomerPropertyLink>().FirstOrDefaultAsync(x => x.Id == id);
        if (link is null) return NotFound();
        db.Remove(link);
        await db.SaveChangesAsync();
        return NoContent();
    }
}


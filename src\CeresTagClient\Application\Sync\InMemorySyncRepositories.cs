using CeresTagClient.Core.Interfaces;

namespace CeresTagClient.Application.Sync;

public class InMemorySyncStateRepository : ISyncStateRepository
{
    private readonly Dictionary<string, DateTime> _last = new(StringComparer.OrdinalIgnoreCase);

    public Task<DateTime?> GetLastSyncAsync(string esn, CancellationToken ct = default)
    {
        return Task.FromResult(_last.TryGetValue(esn, out var v) ? (DateTime?)v : null);
    }

    public Task SetLastSyncAsync(string esn, DateTime whenUtc, CancellationToken ct = default)
    {
        _last[esn] = whenUtc;
        return Task.CompletedTask;
    }
}

public class InMemoryObservationRepository : IObservationRepository
{
    private readonly Dictionary<string, SortedDictionary<DateTime, ObservationEntity>> _store = new(StringComparer.OrdinalIgnoreCase);

    public Task UpsertBatchAsync(IEnumerable<ObservationEntity> observations, CancellationToken ct = default)
    {
        foreach (var o in observations)
        {
            if (!_store.TryGetValue(o.Esn, out var byTime))
            {
                byTime = new SortedDictionary<DateTime, ObservationEntity>();
                _store[o.Esn] = byTime;
            }
            byTime[o.Timestamp] = o;
        }
        return Task.CompletedTask;
    }

    // For tests
    public int CountFor(string esn) => _store.TryGetValue(esn, out var byTime) ? byTime.Count : 0;
}


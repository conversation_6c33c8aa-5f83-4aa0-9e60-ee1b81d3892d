using CeresTagClient.Application.Webhooks;
using CeresTagClient.Core.Interfaces;

namespace CeresTagClient.Tests;

public class WebhookSignatureValidatorTests
{
    private readonly IWebhookSignatureValidator _validator = new HmacSha256SignatureValidator();

    [Fact]
    public void Valid_Signature_Is_Accepted()
    {
        var secret = "shhh";
        var payload = "{\"hello\":\"world\"}";
        var signature = "sha256=" + ComputeHex(secret, payload);
        Assert.True(_validator.IsValid(payload, signature, secret));
    }

    [Fact]
    public void Invalid_Signature_Is_Rejected()
    {
        var secret = "shhh";
        var payload = "{\"hello\":\"world\"}";
        var signature = "sha256=deadbeef";
        Assert.False(_validator.IsValid(payload, signature, secret));
    }

    [Fact]
    public void Wrong_Algo_Is_Rejected()
    {
        var secret = "shhh";
        var payload = "{\"hello\":\"world\"}";
        var signature = "sha1=" + ComputeHex(secret, payload);
        Assert.False(_validator.IsValid(payload, signature, secret));
    }

    private static string ComputeHex(string secret, string payload)
    {
        using var hmac = new System.Security.Cryptography.HMACSHA256(System.Text.Encoding.UTF8.GetBytes(secret));
        var hash = hmac.ComputeHash(System.Text.Encoding.UTF8.GetBytes(payload));
        return Convert.ToHexString(hash).ToLowerInvariant();
    }
}


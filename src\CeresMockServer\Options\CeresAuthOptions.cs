namespace CeresMockServer.Options;

public class CeresAuthOptions
{
    public string Issuer { get; set; } = string.Empty;
    public string Audience { get; set; } = string.Empty;
    public string SigningKey { get; set; } = string.Empty;
    public int TokenExpirySeconds { get; set; } = 7200;
    public List<TestClient> TestClients { get; set; } = new();
}

public class TestClient
{
    public string ClientId { get; set; } = string.Empty;
    public string ClientSecret { get; set; } = string.Empty;
    public string Scope { get; set; } = "read";
}


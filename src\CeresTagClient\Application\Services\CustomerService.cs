using System.Net.Http.Json;
using CeresTagClient.Core.Dtos;
using CeresTagClient.Core.Interfaces;

namespace CeresTagClient.Application.Services;

public class CustomerService(IHttpClientFactory factory) : ICustomerService
{
    private readonly HttpClient _http = factory.CreateClient("CeresApi");

    public async Task<CustomerAuthStartResponse> StartCustomerAuthAsync(string redirectUri, string state, CancellationToken ct = default)
    {
        var payload = new { redirect_uri = redirectUri, state };
        var resp = await _http.PostAsJsonAsync("api/v1/customer-linkage/start", payload, ct);
        resp.EnsureSuccessStatusCode();
        return await resp.Content.ReadFromJsonAsync<CustomerAuthStartResponse>(cancellationToken: ct) ?? new CustomerAuthStartResponse();
    }

    public async Task<CustomerTokenResponse> ExchangeAuthorizationCodeAsync(string code, string state, CancellationToken ct = default)
    {
        var payload = new CustomerAuthCallbackRequestDto { Code = code, State = state };
        var resp = await _http.PostAsJsonAsync("api/v1/customer-linkage/callback", payload, ct);
        if ((int)resp.StatusCode == 400)
        {
            var pd = await resp.Content.ReadFromJsonAsync<ProblemDetailsDto>(cancellationToken: ct);
            throw new CeresTagClient.Core.Exceptions.ValidationFailedException(pd?.Detail ?? await resp.Content.ReadAsStringAsync(ct), pd?.Type);
        }
        resp.EnsureSuccessStatusCode();
        return await resp.Content.ReadFromJsonAsync<CustomerTokenResponse>(cancellationToken: ct) ?? new CustomerTokenResponse();
    }
}


using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CeresMockServer.Controllers;

[ApiController]
[Authorize]
[Route("api/v1/test-webhook")]
public class TestWebhookReceiverController : ControllerBase
{
    private static readonly System.Collections.Concurrent.ConcurrentDictionary<string, System.Collections.Concurrent.ConcurrentQueue<(Dictionary<string, string> Headers, string Body)>> ReceivedByPath = new();

    public static void AddFromSink(string path, Dictionary<string, string> headers, string body)
    {
        var q = ReceivedByPath.GetOrAdd(path, _ => new());
        q.Enqueue((headers, body));
    }

    [HttpPost]
    public async Task<IActionResult> Receive()
    {
        using var reader = new StreamReader(Request.Body);
        var body = await reader.ReadToEndAsync();
        var headers = Request.Headers.ToDictionary(k => k.Key, v => v.Value.ToString());
        var path = "/api/v1/test-webhook";
        AddFromSink(path, headers, body);
        return Ok();
    }

    [HttpPost("{*bucket}")]
    public async Task<IActionResult> ReceiveBucket()
    {
        using var reader = new StreamReader(Request.Body);
        var body = await reader.ReadToEndAsync();
        var headers = Request.Headers.ToDictionary(k => k.Key, v => v.Value.ToString());
        var path = Request.Path.ToString();
        AddFromSink(path, headers, body);
        return Ok();
    }

    [HttpGet("inspect")]
    public IActionResult Inspect([FromQuery] string? path)
    {
        if (!string.IsNullOrWhiteSpace(path))
        {
            if (ReceivedByPath.TryGetValue(path, out var qForPath))
            {
                var arr = qForPath.ToArray().Select(x => new { x.Headers, x.Body }).ToList();
                return Ok(arr);
            }
            // If a specific path is requested but not present, return empty set
            return Ok(Array.Empty<object>());
        }
        var items = ReceivedByPath.SelectMany(kv => kv.Value.ToArray()).Select(x => new { x.Headers, x.Body }).ToList();
        return Ok(items);
    }

    [HttpDelete]
    public IActionResult Clear([FromQuery] string? path)
    {
        if (!string.IsNullOrWhiteSpace(path))
        {
            ReceivedByPath.TryRemove(path, out _);
        }
        else
        {
            ReceivedByPath.Clear();
        }
        return NoContent();
    }
}


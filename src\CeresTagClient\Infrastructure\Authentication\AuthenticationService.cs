using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using CeresTagClient.Core.Dtos;
using CeresTagClient.Core.Interfaces;
using CeresTagClient.Core.Options;
using Microsoft.Extensions.Options;

namespace CeresTagClient.Infrastructure.Authentication;

public class AuthenticationService : IAuthenticationService
{
    private readonly HttpClient _http;
    private readonly CeresApiOptions _options;
    private readonly SemaphoreSlim _semaphore = new(1, 1);
    private TokenResponse? _cached;
    private DateTime _cachedAtUtc;

    public AuthenticationService(HttpClient http, IOptions<CeresApiOptions> options)
    {
        _http = http;
        _options = options.Value;
    }

    public async Task<string> GetAccessTokenAsync(CancellationToken ct = default)
    {
        await _semaphore.WaitAsync(ct);
        try
        {
            if (_cached is not null && !IsExpired(_cached))
            {
                return _cached.AccessToken;
            }

            var content = new StringContent(
                JsonSerializer.Serialize(new
                {
                    grant_type = "client_credentials",
                    client_id = _options.ClientId,
                    client_secret = _options.ClientSecret
                }), Encoding.UTF8, "application/json");

            using var request = new HttpRequestMessage(HttpMethod.Post, _options.TokenEndpoint)
            {
                Content = content
            };

            using var response = await _http.SendAsync(request, ct);
            response.EnsureSuccessStatusCode();

            var payload = await response.Content.ReadAsStringAsync(ct);
            _cached = JsonSerializer.Deserialize<TokenResponse>(payload) ?? new TokenResponse();
            _cachedAtUtc = DateTime.UtcNow;
            return _cached.AccessToken;
        }
        finally
        {
            _semaphore.Release();
        }
    }

    private bool IsExpired(TokenResponse token)
    {
        if (string.IsNullOrEmpty(token.AccessToken) || token.ExpiresIn <= 0)
            return true;
        // Refresh 2 minutes early
        var expiresAt = _cachedAtUtc.AddSeconds(token.ExpiresIn - 120);
        return DateTime.UtcNow >= expiresAt;
    }
}


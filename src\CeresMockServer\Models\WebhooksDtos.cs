
namespace CeresMockServer.Models;

public record WebhookCreateRequest
{
    public string Url { get; init; } = string.Empty;
    public string Type { get; init; } = string.Empty;
    public string SharedSecret { get; init; } = string.Empty;
    public string? Metadata { get; init; }
    public bool Active { get; init; } = true;
}

public record WebhookUpdateRequest
{
    public string? Url { get; init; }
    public string? Type { get; init; }
    public string? SharedSecret { get; init; }
    public string? Metadata { get; init; }
    public bool? Active { get; init; }
}

public record WebhookResponse
{
    public long Id { get; init; }
    public string Url { get; init; } = string.Empty;
    public string Type { get; init; } = string.Empty;
    public bool Active { get; init; }
    public string? Metadata { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime UpdatedAt { get; init; }
}


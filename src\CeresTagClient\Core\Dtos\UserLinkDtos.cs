using System.Text.Json.Serialization;

namespace CeresTagClient.Core.Dtos;

public class AvailablePropertyDto
{
    [JsonPropertyName("property_id")] public string PropertyId { get; set; } = string.Empty;
    [JsonPropertyName("property_name")] public string PropertyName { get; set; } = string.Empty;
}

public class UserLinkGrantsAccessResponse
{
    [JsonPropertyName("existing_links")] public List<CustomerLinkResponse> ExistingLinks { get; set; } = new();
    [JsonPropertyName("available_links")] public List<AvailablePropertyDto> AvailableLinks { get; set; } = new();
}

public class FailedLink
{
    [JsonPropertyName("property_id")] public string PropertyId { get; set; } = string.Empty;
    [JsonPropertyName("reason")] public string Reason { get; set; } = string.Empty;
}

public class UserLinkAddLinksResponse
{
    [JsonPropertyName("created")] public List<CustomerLinkResponse> Created { get; set; } = new();
    [JsonPropertyName("failed")] public List<FailedLink> Failed { get; set; } = new();
}


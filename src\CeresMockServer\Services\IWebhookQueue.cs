using System.Collections.Concurrent;
using System.Text.Json;
using CeresMockServer.Data;

namespace CeresMockServer.Services;

public record WebhookMessage(long RegistrationId, string Url, string SharedSecret, string Type, string Payload, DateTime TimestampUtc);

public interface IWebhookQueue
{
    void Enqueue(WebhookMessage message);
    bool TryDequeue(out WebhookMessage? message);
}

public class InMemoryWebhookQueue : IWebhookQueue
{
    private readonly ConcurrentQueue<WebhookMessage> _queue = new();

    public void Enqueue(WebhookMessage message) => _queue.Enqueue(message);
    public bool TryDequeue(out WebhookMessage? message)
    {
        var ok = _queue.TryDequeue(out var m);
        message = m;
        return ok;
    }
}


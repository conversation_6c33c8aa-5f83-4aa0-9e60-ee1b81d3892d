## Webhook Data Flow Diagram (DFD)

By default all live devices send Alert or Pasture Feed Intake (PFI) daily summary data to your configured webhook/s.



A device will send an alert as soon as a set of pre-programmed conditions is met. After a device is activated, these conditions are constantly evaluated and can trigger at any time.



The Daily Pasture Feed Intake (PFI) packet is sent once a day between midnight and 6am (local time based on where the active device is located).



The ability for your solution to receive, process and notify a customer of device alerts is one of the [minimum requirements](../Minimum_Requirements.pdf) of being an Approved Software Partner.

<kbd><img src="https://user-images.githubusercontent.com/1161583/186303521-5cd35b91-7009-4859-86f1-236abd160576.jpg" /></kbd>


namespace CeresMockServer.Options;

public class ResponseSimulationOptions
{
    // Global fixed delay in milliseconds applied to all requests (0 disables)
    public int DefaultDelayMs { get; set; } = 0;

    // Adds up to this many milliseconds of random jitter on top of DefaultDelayMs
    public int RandomDelayMax { get; set; } = 0;

    // Optional header that, if present and set to "true", bypasses the delay
    public string BypassHeaderName { get; set; } = "X-Bypass-Delay";
}


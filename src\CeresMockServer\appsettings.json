{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"CeresDb": "Data Source=ceres-mock.db"}, "CeresMock": {"Features": {"AlertsEnabled": true}, "Authentication": {"Issuer": "https://mock.cerestag.com", "Audience": "ceres-api", "SigningKey": "dev-super-secret-signing-key-change-me", "TokenExpirySeconds": 7200, "TestClients": [{"ClientId": "test-client-1", "ClientSecret": "test-secret-1", "Scope": "read write"}, {"ClientId": "test-client-2", "ClientSecret": "test-secret-2", "Scope": "read"}]}, "CustomerAuth": {"Issuer": "https://mock.cerestag.com/customer", "Audience": "ceres-customer", "SigningKey": "dev-customer-signing-key-change-me", "TokenExpirySeconds": 3600, "DefaultRedirectUri": "http://localhost/callback"}}}
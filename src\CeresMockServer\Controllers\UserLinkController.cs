using CeresMockServer.Data;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CeresMockServer.Controllers;

/// <summary>
/// UserLink Grants endpoints (mock). Provides access and linking flows used by software partner integrations.
/// </summary>
[ApiController]
[Authorize]
[Route("api/v1/userlink/grants")]
[Produces("application/json")]
public class UserLinkController(CeresDbContext db) : ControllerBase
{
    /// <summary>
    /// Returns existing and available property links for a given grant id (mock: customer id).
    /// </summary>
    /// <param name="grantId">Grant identifier (mock: use customer id).</param>
    /// <response code="200">Returns existing and available links.</response>
    /// <response code="400">Validation failed.</response>
    /// <response code="401">Unauthorized.</response>
    [HttpGet("access")]
    [ProducesResponseType(typeof(UserLinkGrantsAccessResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetAccess([FromQuery] string grantId, CancellationToken ct)
    {
        if (string.IsNullOrWhiteSpace(grantId))
            return BadRequest(new ProblemDetails { Title = "validation_failed", Detail = "grantId is required", Status = StatusCodes.Status400BadRequest, Type = "about:blank" });

        // Mock: ensure customer exists (access is tied to existing or new customer)
        var customer = await db.Customers.FindAsync([grantId], ct);
        if (customer is null)
        {
            customer = new Customer { Id = grantId, DisplayName = $"Customer {grantId}" };
            db.Add(customer);
            await db.SaveChangesAsync(ct);
        }

        // Existing links for this customer
        var existing = await db.CustomerPropertyLinks.AsNoTracking()
            .Where(x => x.CustomerId == grantId)
            .OrderBy(x => x.PropertyId)
            .Select(x => new CustomerLinkResponse
            {
                Id = x.Id,
                CustomerId = x.CustomerId,
                PropertyId = x.PropertyId,
                PropertyName = x.PropertyName,
                LinkedAt = x.LinkedAt
            })
            .ToListAsync(ct);

        // Available links: in this mock, return empty; could be populated from a seeded list
        var response = new UserLinkGrantsAccessResponse
        {
            ExistingLinks = existing,
            AvailableLinks = new List<AvailablePropertyDto>()
        };
        return Ok(response);
    }

    /// <summary>
    /// Adds property links for the specified property_grant_id (mock: customer id).
    /// </summary>
    /// <response code="200">Created and failed links summary.</response>
    /// <response code="400">Validation failed.</response>
    /// <response code="401">Unauthorized.</response>
    [HttpPost("addlinks")]
    [ProducesResponseType(typeof(UserLinkAddLinksResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> AddLinks([FromBody] UserLinkAddLinksRequest request, CancellationToken ct)
    {
        if (request == null)
            return Problem(title: "validation_failed", detail: "Body is required", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
        if (string.IsNullOrWhiteSpace(request.PropertyGrantId))
            return Problem(title: "validation_failed", detail: "property_grant_id is required", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");
        if (request.Links == null || request.Links.Count == 0)
            return Problem(title: "validation_failed", detail: "links array is required", statusCode: StatusCodes.Status400BadRequest, type: "about:blank");

        // Ensure a customer exists for this grant id
        var cust = await db.Customers.FindAsync([request.PropertyGrantId], ct);
        if (cust is null)
        {
            cust = new Customer { Id = request.PropertyGrantId, DisplayName = $"Customer {request.PropertyGrantId}" };
            db.Add(cust);
            await db.SaveChangesAsync(ct);
        }

        var resp = new UserLinkAddLinksResponse();
        foreach (var item in request.Links)
        {
            if (string.IsNullOrWhiteSpace(item.PropertyId))
            {
                resp.Failed.Add(new FailedLink { PropertyId = item.PropertyId, Reason = "invalid_property_id" });
                continue;
            }

            var exists = await db.CustomerPropertyLinks.AsNoTracking().AnyAsync(x => x.CustomerId == request.PropertyGrantId && x.PropertyId == item.PropertyId, ct);
            if (exists)
            {
                resp.Failed.Add(new FailedLink { PropertyId = item.PropertyId, Reason = "already_linked" });
                continue;
            }

            var link = new CustomerPropertyLink
            {
                CustomerId = request.PropertyGrantId,
                PropertyId = item.PropertyId,
                PropertyName = string.IsNullOrWhiteSpace(item.PropertyName) ? item.PropertyId : item.PropertyName
            };
            db.Add(link);
            await db.SaveChangesAsync(ct);

            resp.Created.Add(new CustomerLinkResponse
            {
                Id = link.Id,
                CustomerId = link.CustomerId,
                PropertyId = link.PropertyId,
                PropertyName = link.PropertyName,
                LinkedAt = link.LinkedAt
            });
        }

        return Ok(resp);
    }
}


using CeresMockServer.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CeresMockServer.Controllers;

[ApiController]
[Route("api/v1/webhooks/admin")]
[Authorize]
public class WebhookAdminController(WebhookFailureRuntime runtime) : ControllerBase
{
    public record FailureSettingsRequest(bool? ForceFailure, int? RandomFailurePercent);

    [HttpGet("failures")]
    public IActionResult GetFailures()
        => Ok(new { runtime.Value.ForceFailure, runtime.Value.RandomFailurePercent });

    [HttpPost("failures")]
    public IActionResult SetFailures([FromBody] FailureSettingsRequest req)
    {
        runtime.Set(req.ForceFailure, req.RandomFailurePercent);
        return NoContent();
    }
}


# Postman Bearer token example

If you are having trouble figuring out how to get the authentication to work with the Bear<PERSON> token we have included some screenshots below of how to generate it using Postman (https://www.postman.com/).



1. You will need:

- **API Client_ID & Secret** from your CERES PORTAL software account. You can generate these **OAuth2 credentials (API Client_ID & Secret)** through your [Ceres Portal software account dashboard ](https://test.cerestag.com/dashboard)
- Base64 encoded **API Client_ID & Secret** of the format Client_ID:Secret, see [Python 39 example](Py39Base64Example.md)

2. Use the Base64 encoded client_id:secret to generate the bearer token (see example images below).

3. Copy the *access_token* as this is your **bearer token** and can be used to query the API. For more details see [Postman API Example](PostmanAPIExample.md)



**NOTE: The example below uses the URL for our TEST environment only, you will have to modify the URL for production when you move across.**

## Header Configuration

<img src="https://user-images.githubusercontent.com/1161583/*********-6fea2a8f-6cd3-40e9-b552-ebd912f111b0.png">



## Body Configuration

<img src="https://user-images.githubusercontent.com/1161583/*********-0c2bd890-0c01-4f79-b197-aa8c1ea2b7a4.png">
using System.Text.Json.Serialization;

namespace CeresTagClient.Core.Dtos;

public class HistoricalRequestResponse
{
    [JsonPropertyName("requestId")] public string RequestId { get; set; } = string.Empty;
}

public class HistoricalRetrieveResponse
{
    [JsonPropertyName("status")] public string Status { get; set; } = string.Empty;
    [JsonPropertyName("data")] public List<RecentObservation> Data { get; set; } = new();
    [JsonPropertyName("pagination")] public PaginationMeta Pagination { get; set; } = new();
}


# CERES PORTAL Initiated - Customer Linkage User Interface



When a customer purchases devices, they are asked to assign the order to a location and link a software solution to the location. 
This solution will then be able to ingest data for all devices associated with this location.

<img src="https://user-images.githubusercontent.com/1161583/*********-3ec15e45-8b95-4731-b83a-eebb6eab1723.png" />



### Verify Software Account

After a customer selects their software solution from the drop down list (e.g. TEST Software in image below), they have to confirm they have an account with this solution by selecting the VERIFY button and logging into the [**Software Partner Customer Authentication Endpoint**](../CeresPortal_SoftwareAccount_UI.md) that is launched in a new tab.

<img src="https://user-images.githubusercontent.com/1161583/*********-adf586c2-90b7-4a5e-9400-657a18e02adc.png" />

### **Launch Software Partner Customer Authentication Endpoint**

When the customer hits verify, the [**Software Partner Customer Authentication Endpoint**](../CeresPortal_SoftwareAccount_UI.md) is launched and the customer is expected to login within 5 minutes.

<img src="https://user-images.githubusercontent.com/1161583/*********-1b421438-3b55-40bd-93d6-3ef069d915a8.png" />

When the **Software Partner Customer Authentication Endpoint** is launched some additional parameters are included in the GET request.

**Example Software Partner Customer Authentication URL**:
https://myTESTSoftwareEndpoint/login?response_type=token&client_id=SWCustomerAuth3Value&state=randomlyGeneratedGUID&redirect_uri=https%3A%2F%2Fextapi.test.cerestag.com%2FSoftwareVendor%2FUserAuthentication%2FSigninCallback

The URL above contains 5 variables:

- ***Software Partner Customer Authentication Endpoint*** - [https://myTESTSoftwareEndpoint] - defined in the Customer Linkage section of your CERES PORTAL Software Account.
- ***Response Type*** - [response_type=token] - the type of response expected from the endpoint. This will always have a value of token
- ***Ceres Portal Auth Client ID*** - [client_id=SWCustomerAuth3Value] - a variable defined by you in the [**Ceres Portal Software Account**](../CeresPortal_SoftwareAccount_UI.md).
- ***State*** - [state=randomlyGeneratedGUID] - This is a unique code generated by the CERES PORTAL and used to verify connection requests from system to system. It has a timeout and will change on each request.
- ***Ceres Portal signin callback endpoint*** - [redirect_uri=https://.....] - This is the URI your system is directed to after a customer successfully logs in, this is automatically populated by the CERESPORTAL when the **Software Partner Customer Authentication Endpoint** is launched. For more details on this see below or visit the [Customer Linkage Data Flow Diagram](DFD.md).

<img src="https://user-images.githubusercontent.com/1161583/*********-f5afafbf-f9c4-4714-826d-983c2a60866a.png">



### System to System Communication

After a customer logs into the **Software Partner Customer Authentication Endpoint** your solution needs to send a **GET** request to the **CERES PORTAL signin callback endpoint** (the redirect_uri) with some variables.

<img src="https://user-images.githubusercontent.com/1161583/190553532-5220d411-ad0e-45f3-a77b-bcd47d4b7cd0.png">



**Example CERES PORTAL signin callback endpoint URL**:

https://redirect_uri?access_token=MyJWTAccessToken&id_token=MyRandomJWT&expires_in=300&token_type=Bearer&state=stateSentToSoftwarePartnerCustomerAuthenticationEndpoint

The URL above contains 6 variables:

- ***redirect_uri*** - This is the CERES PORTAL signin callback endpoint specified in section above "Launch Software Partner Customer Authentication Endpoint".
- ***access_token*** - This is mandatory and is a JWT token (https://jwt.io/) specified by your solution specific for the customer that just authenticated
- ***expires_in*** - This is mandatory and puts an expiry on the request in seconds. typical value is 300
- ***token_type*** - This is mandatory. The type of token used to authenticate. This will always by "Bearer" e.g. token_type=Bearer
- ***state*** - This is a unique code generated by the CERES PORTAL and used to verify connection requests from system to system. It has a timeout and will change on each request. It was passed to your solution in the section above "Launch Software Partner Customer Authentication Endpoint"
- ***id_token*** - This is optional and is a JWT token (https://jwt.io/) that can be included in your response, but our system does not do anything with it.

Once these variables are received and validated by the CERES PORTAL, a **GET** request is sent to your **Software Partner Customer Account Identifiers Endpoint** with the customers **access_token** as an authorization header ("Authorization", $"Bearer {access_token}"). This endpoint validates the access_token before returning a relevant List of Identifiers for the autheticated customer.

<img src="https://user-images.githubusercontent.com/1161583/*********-a44c4c99-d031-4106-90bd-b5ee64e7022d.png">



An example JSON formatted list expected by the Ceres Portal:

[
{"DisplayName" : "Location 1 for customer", "Identifier" : "UniqueHexForLocation1"},
{"DisplayName" : "Location 2 for customer", "Identifier" : "UniqueHexForLocation2"}
]

> **!!! NOTE:** **Identifier** is a string defined by a software partner and MUST NOT contain any **Personally Identifiable Information (PII)**

Once the JSON formatted list is received by the CERES PORTAL, the launched tab closes and the customer is returned to their *Connect Software* Page.



### Software Verified

The account has now been now verified.

If the customer has more than one account in your solution (multiple farms/locations/accounts), then they should be presented with a list of options to choose from based on the JSON formatted list.

When the customer saves this account, Customer Linkage is complete.

<img src="https://user-images.githubusercontent.com/1161583/*********-d5ac7987-512e-4e4a-a928-11e226a627b9.png" />
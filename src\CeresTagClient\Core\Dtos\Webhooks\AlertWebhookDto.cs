namespace CeresTagClient.Core.Dtos.Webhooks;

public class AlertWebhookDto
{
    public string type { get; set; } = string.Empty;
    public string version { get; set; } = string.Empty;
    public string alert_type { get; set; } = string.Empty;
    public string esn { get; set; } = string.Empty;
    public DateTime timestamp { get; set; }
    public string severity { get; set; } = string.Empty;
    public LocationDto? location { get; set; }
    public int? battery_level { get; set; }
    public ActivitySummaryDto? activity_summary { get; set; }

    public class LocationDto { public double lat { get; set; } public double lon { get; set; } }
    public class ActivitySummaryDto { public int window_minutes { get; set; } public double ratio { get; set; } }
}


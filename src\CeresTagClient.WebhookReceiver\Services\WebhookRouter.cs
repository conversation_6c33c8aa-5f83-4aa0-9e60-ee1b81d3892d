using System.Text.Json;

namespace CeresTagClient.WebhookReceiver.Services;

public interface IWebhookRouter
{
    bool TryGetType(string payload, out string type);
    bool TryResolveHandler(string type, out IWebhookHandler handler);
}

public class WebhookRouter : IWebhookRouter
{
    private readonly Dictionary<string, IWebhookHandler> _handlers;

    public WebhookRouter(IEnumerable<IWebhookHandler> handlers)
    {
        _handlers = handlers.ToDictionary(h => h.Type, StringComparer.OrdinalIgnoreCase);
    }

    public bool TryGetType(string payload, out string type)
    {
        type = string.Empty;
        try
        {
            using var doc = JsonDocument.Parse(payload);
            if (doc.RootElement.TryGetProperty("type", out var t) && t.ValueKind == JsonValueKind.String)
            {
                type = t.GetString() ?? string.Empty;
                return !string.IsNullOrWhiteSpace(type);
            }
        }
        catch { }
        return false;
    }

    public bool TryResolveHandler(string type, out IWebhookHandler handler)
    {
        return _handlers.TryGetValue(type, out handler!);
    }
}


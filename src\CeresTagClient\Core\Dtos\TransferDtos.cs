using System.Text.Json.Serialization;

namespace CeresTagClient.Core.Dtos;

public class TagTransferResponseDto
{
    [JsonPropertyName("transferred")] public List<string> Transferred { get; set; } = new();
    [JsonPropertyName("failed")] public List<TagTransferFailureDto> Failed { get; set; } = new();
}

public class TagTransferFailureDto
{
    [JsonPropertyName("esn")] public string Esn { get; set; } = string.Empty;
    [JsonPropertyName("reason")] public string Reason { get; set; } = string.Empty;
}


using System.Text.Json.Serialization;

namespace CeresMockServer.Models;

public class TagTransferRequest
{
    [JsonPropertyName("esns")] public List<string> Esns { get; set; } = new();
    [JsonPropertyName("softwareIdentifier")] public string SoftwareIdentifier { get; set; } = string.Empty;
    [JsonPropertyName("propertySoftwareId")] public string PropertySoftwareId { get; set; } = string.Empty;
    [JsonPropertyName("propertyName")] public string? PropertyName { get; set; }
}

public class TagTransferResult
{
    [JsonPropertyName("transferred")] public List<string> Transferred { get; set; } = new();
    [JsonPropertyName("failed")] public List<TagTransferFailure> Failed { get; set; } = new();
}

public class TagTransferFailure
{
    [JsonPropertyName("esn")] public string Esn { get; set; } = string.Empty;
    [JsonPropertyName("reason")] public string Reason { get; set; } = string.Empty;
}


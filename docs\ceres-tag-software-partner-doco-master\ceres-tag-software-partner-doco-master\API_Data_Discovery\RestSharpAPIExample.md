# Example API Code in C# using the RestSharp NuGet library.



## Overview
This is a simplified example to get an auth token and call a CERES PORTAL endpoint.

## Dependencies
The code runs on .NET Core and requires [RestSharp](https://restsharp.dev/).

## Sample Code
```C#
// Author: <PERSON> (QualityCatalyst)
// Date: February 2021
// Description: A simplified example to get an auth token and call a Ceres Portal endpoint

using RestSharp;
using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Web;

/// <summary>
/// Calls the First Contact endpoint to confirm that authentication works and we can retrieve data from Ceres Portal.
/// Returns <c>null</c> if the endpoint responses with a non-2xx HTTP status code.
/// </summary>
public async Task<string> CallAuthEndpointAsync(string clientID, string secret)
{
    // First, we need an auth token.
    var tokenInfo = await GetAuthTokenAsync(clientID, secret).ConfigureAwait(false);
    // Now that we have the token, we can call a Ceres Portal endpoint.
    var externalApiClient = new HttpClient();
    externalApiClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {tokenInfo.AccessToken}");
    var response = await externalApiClient.GetAsync("https://extapi.test.cerestag.com/FirstContact/CheckAuthentication").ConfigureAwait(false);
    // We expect a 200 OK. If we get any 2xx back, we return the body of the response message.
    return response.IsSuccessStatusCode
        ? await response.Content.ReadAsStringAsync().ConfigureAwait(false)
        : null;
}

/// <summary>
/// Generate Bearer token using client ID and secret.
/// </summary>
private async Task<TokenObject> GetAuthTokenAsync(string clientID, string secret)
{
    string BaseAuthEndPoint = "https://testcerestag.auth.ap-southeast-2.amazoncognito.com/"; // test endpoint only
    string TokenEndPoint = BaseAuthEndPoint + "oauth2/token";
    var base64Token = GenerateBase64Token(clientID, secret); // Base64 encode clientId and secret
    var scope = HttpUtility.UrlEncode("https://www.cerestag.com/Ceres Portal/tags.read");
    var client = new RestClient(TokenEndPoint);
    var request = new RestRequest(Method.POST);
    request.AddHeader("cache-control", "no-cache");
    request.AddHeader("content-type", "application/x-www-form-urlencoded");
    request.AddHeader("authorization", $"Basic {base64Token}");
    request.AddParameter("application/x-www-form-urlencoded", $"grant_type=client_credentials&scope={scope}", ParameterType.RequestBody);
    var response = await client.ExecuteAsync(request).ConfigureAwait(false);
    return JsonSerializer.Deserialize<TokenObject>(response.Content);
}

/// <summary>
/// Generates a Base64 token including the client ID and secret, so it can be used for basic authentication.
/// </summary>
private string GenerateBase64Token(string clientId, string clientSecret)
    => Convert.ToBase64String(Encoding.UTF8.GetBytes($"{clientId}:{clientSecret}"));

/// <summary>
/// Structure of a serializable token object.
/// </summary>
public class TokenObject
{
    [JsonPropertyName("access_token")]
    public string AccessToken { get; set; } // Bearer token
    [JsonPropertyName("token_type")]
    public string TokenType { get; set; } // token type - Bearer
    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; set; } // timeout of the token
}
```


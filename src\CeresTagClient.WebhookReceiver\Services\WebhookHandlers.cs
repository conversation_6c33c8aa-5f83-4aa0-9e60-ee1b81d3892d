using System.Text.Json;
using CeresTagClient.Core.Dtos.Webhooks;
using CeresTagClient.Core.Interfaces;

namespace CeresTagClient.WebhookReceiver.Services;

public interface IWebhookHandler
{
    string Type { get; }
    Task HandleAsync(string jsonPayload, CancellationToken ct = default);
}

public class AlertWebhookHandler(IWebhookProcessor processor) : IWebhookHandler
{
    public string Type => "alert";
    public Task HandleAsync(string jsonPayload, CancellationToken ct = default)
    {
        var dto = JsonSerializer.Deserialize<AlertWebhookDto>(jsonPayload)!;
        return processor.ProcessAlertAsync(dto, ct);
    }
}

public class PfiWebhookHandler(IWebhookProcessor processor) : IWebhookHandler
{
    public string Type => "pfi";
    public Task HandleAsync(string jsonPayload, CancellationToken ct = default)
    {
        var dto = JsonSerializer.Deserialize<PfiWebhookDto>(jsonPayload)!;
        return processor.ProcessPfiAsync(dto, ct);
    }
}

public class HistoricalWebhookHandler(IWebhookProcessor processor) : IWebhookHandler
{
    public string Type => "historical";
    public Task HandleAsync(string jsonPayload, CancellationToken ct = default)
    {
        var dto = JsonSerializer.Deserialize<HistoricalWebhookDto>(jsonPayload)!;
        return processor.ProcessHistoricalAsync(dto, ct);
    }
}

public class StandardWebhookHandler(IWebhookProcessor processor) : IWebhookHandler
{
    public string Type => "standard";
    public Task HandleAsync(string jsonPayload, CancellationToken ct = default)
    {
        var dto = JsonSerializer.Deserialize<StandardWebhookDto>(jsonPayload)!;
        return processor.ProcessStandardAsync(dto, ct);
    }
}


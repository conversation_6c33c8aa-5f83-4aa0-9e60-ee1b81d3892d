# FirmwareVersions

The *FirmwareVersion* of a device can be determined via the [GetTagDetailsSince](https://github.com/cerestag/ceres-tag-software-partner-doco/blob/master/API_Data_Discovery/APIDoco.md#get-v1tagdetailsgettagdetailssince) API endpoint.

#### firmwareVersion >= **64.4.0.2 & 64.5.1.2**
    CERES TRACE OR CERES RANCH devices purchased after April 2023 and CERES RANCHER devices(64.5.1.2) purchased after May 2025 with Pasture Feed Intake (PFI) Algorithm installed. The PFI Algorithm was designed specifically for cattle. If devices are not installed on cattle the accuracy of the behaviour classification is affected.
  	
    	Fields = [Location_Accuracy, Grazing_Minutes, Temperature, Battery, Latitude, Longitude, Resting/Ruminating_Minutes, Walking_Minutes, Other_Minutes]
    	  
	  	Location_Accuracy: How accurate the Latitude & Longitude reading in the data packet is
				0	<= 2m
				1	<= 5m
				2	<= 10m
				3	<= 25m
				4	<= 50m
				5	<=100m
				6	> 100m
				7	No Fix
				
		Grazing_Minutes: The number of minutes spent grazing in the last 6 hours, rounded to the nearest 24 minutes.
			
		Temperature: Device temperature in Celcius. This value ranges from 0 - 75 and increments in 5 degree blocks. The device will continue to operate in temperatures down to -20, however due to size limitations for the data packet, the lowest value will be 0.
			
		Battery: (optional) This value is not mandatory, but provides an estimated battery percentage of the device out of 100%
			
		Latitude: Latitude of device when data sent
  		
  		Longitude: Longitude of device when data sent
  		
  		Resting/Ruminating_Minutes: The number of minutes spent resting OR ruminating in the last 6 hours, rounded to the nearest 24 minutes. 
  		
  		Walking_Minutes: The number of minutes spent walking in the last 6 hours, rounded to the nearest 24 minutes.
  		
  		Other_Minutes: The number of minutes spent doing other activities (behaviour not classified as grazing, ruminating, resting or walking) in the last 6 hours.
  	
  	--


  	
#### firmwareVersion = 63.3.0.2
  	
  	CeresWild devices purchased AFTER April 2023 with Advanced Activity Algorithm installed.
	  
  		Fields = [Location_Accuracy, Activity_LatestHour, Temperature, Battery, Latitude, Longitude, Activity_PreviousHour, NULL, NULL]
    	
    	Location_Accuracy: How accurate the Latitude & Longitude reading in the data packet is
  			0	<= 2m
    		1	<= 5m
    		2	<= 10m
    		3	<= 25m
    		4	<= 50m
  			5	<=100m
    		6	> 100m
  			7	No Fix
  		
  		Activity_LatestHour: The intensity of physical activity from 0 - 63 (e.g. a paint can sitting on the spot would be a 1 vs a paint can on the same spot in a vibrating paint shaker would be a 63) 0 - 60 minutes before the data packet was sent. This allows you to compare intensity of physical activity across different animals.
  		
  		Temperature: Device temperature in Celcius. This value ranges from 0 - 75 and increments in 5 degree blocks. The tag will continue to operate in temperatures down to -20, however due to size limitations for the data packet, the lowest value will be 0.
  		
  		Battery:(optional) This value is not mandatory, but provides an estimated battery percentage of the device out of 100%.
  		
  		Latitude: Latitude of tag when data sent	
  		
  		Longitude: Longitude of tag when data sent
  		
  		Activity_PreviousHour: The intensity of physical activity from 0 - 63 (e.g. a paint can sitting on the spot would be a 1 vs a paint can on the same spot in a vibrating paint shaker would be a 63) 60 - 120 minutes before the data packet was sent. This allows you to compare intensity of physical activity across different animals.
  		
  		NULL: This value is currently null and can be ignored. At a later point in time it may contain a data value. 
  		
  		NULL: This value is currently null and can be ignored. At a later point in time it may contain a data value.

  	--


  	
#### firmwareVersion = 64.1.6.0 or empty
  	
  	CeresTrace OR CeresRanch devices purchased before April 2023.
  	
  		Fields = [Location_Accuracy, Activity1, Temperature, Battery, Latitude, Longitude, Activity2, Activity3, Activity4]
  		
  		Location_Accuracy: How accurate the Latitude & Longitude reading in the data packet is
	  		0	<= 2m
  			1	<= 5m
  			2	<= 10m
  			3	<= 25m
  			4	<= 50m
  			5	<=100m
  			6	> 100m
  			7	No Fix
  			
  		Activity:The intensity of physical activity that has been quantified from 1 - 7 (e.g. a paint can sitting on the spot would be a 1 vs a paint can on the same spot in a vibrating paint shaker would be a 7)

  		There are 4 activity values that come through in the standard data packet.
  			Activity1: Activity value for the most recent hour (now -> 60 minutes ago)
  			Activity2: Activity value for one hour earlier (60 -> 120 minutes ago)
  			Activity3: Activity value for 2 hours earlier (120 -> 180 minutes ago)
  			Activity4: Activity value for 3 hours earlier (180 -> 240 minutes ago)
  		
  			Activity values can be interpreted as:
  				0 Error getting Activity
  				1 No Activity
  				2 Low activity
  				3-4 Medium activity level
  				5-6 High activity levels
  				7 Extremely high activity levels
  		
  		Temperature: Device temperature in Celcius. This value ranges from 0 - 75 and increments in 5 degree blocks. The tag will continue to operate in temperatures down to -20, however due to size limitations for the data packet, the lowest value will be 0.
  		
  		Battery:(optional) This value is not mandatory to display, but provides an estimated battery percentage of the device out of 100%.
  		
  		Latitude: Latitude of tag when data sent	
  		
  		Longitude: Longitude of tag when data sent
  	
  	--


  	
#### firmwareVersion = 63.2.0.0 or is empty
  	
  	CeresWild devices purchased before April 2023.
  	
  		Fields = [Location_Accuracy, Activity1, Temperature, NULL, Latitude, Longitude, Activity2, Activity3, Activity4]
  		
  		Location_Accuracy: How accurate the Latitude & Longitude reading in the data packet is
  			0	<= 2m
  			1	<= 5m
  			2	<= 10m
  			3	<= 25m
  			4	<= 50m
  			5	<=100m
  			6	> 100m
  			7	No Fix 
  		  
  		Activity: The intensity of physical activity that has been quantified from 1 - 7 (e.g. a paint can sitting on the spot would be a 1 vs a paint can on the same spot in a vibrating paint shaker would be a 7)
  			
        There are 4 activity values that come through in the standard data packet.
  			Activity1: Activity value for the most recent hour (now -> 60 minutes ago)
  			Activity2: Activity value for one hour earlier (60 -> 120 minutes ago)
  			Activity3: Activity value for 2 hours earlier (120 -> 180 minutes ago)
  			Activity4: Activity value for 3 hours earlier (180 -> 240 minutes ago)
  		
  			Activity can be interpreted as:
  				0 Error getting Activity
  				1 No Activity
  				2 Low activity
  				3-4 Medium activity level
  				5-6 High activity levels
  				7 Extremely high activity levels
  		
  		Temperature: Device temperature in Celcius. This value ranges from **negative 20 to +55** and increments in **5 degree blocks**.
  		
  		Battery:(optional)* This value is not mandatory to display, but provides an estimated battery percentage of the device out of 100%. 
  		
  		Latitude: Latitude of tag when data sent
  		
  		Longitude: Longitude of tag when data sent
using CeresTagClient.Core.Dtos;
using CeresTagClient.Core.Interfaces;
using CeresTagClient.Core.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace CeresTagClient.Application.Sync;

public interface ISyncOrchestrator
{
    Task<int> RunOnceAsync(CancellationToken ct = default);
}

public class SyncOrchestrator(
    ITagService tagService,
    ISyncStateRepository stateRepo,
    IObservationRepository obsRepo,
    IOptions<SyncOptions> options,
    ILogger<SyncOrchestrator> logger) : ISyncOrchestrator
{
    private readonly SyncOptions _opts = options.Value;

    public async Task<int> RunOnceAsync(CancellationToken ct = default)
    {
        if (_opts.Esns.Count == 0)
        {
            logger.LogInformation("Sync disabled or no ESNs configured");
            return 0;
        }

        int total = 0;
        foreach (var esn in _opts.Esns)
        {
            ct.ThrowIfCancellationRequested();

            var from = await stateRepo.GetLastSyncAsync(esn, ct)
                      ?? DateTime.UtcNow.AddHours(-_opts.InitialLookbackHours);

            var page = 1;
            var pagesFetched = 0;
            var batch = new List<ObservationEntity>(_opts.BatchSize);

            while (!ct.IsCancellationRequested)
            {
                if (pagesFetched >= _opts.MaxPagesPerRun) break;

                RecentTagDataResponse recent;
                try
                {
                    recent = await tagService.GetRecentTagDataAsync(esn, from, _opts.PageSize, page, ct);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error fetching recent data for {Esn} page {Page}", esn, page);
                    break;
                }

                if (recent.Data.Count == 0) break;

                foreach (var r in recent.Data)
                {
                    batch.Add(new ObservationEntity
                    {
                        Esn = r.Esn,
                        Timestamp = r.Timestamp,
                        Latitude = r.Latitude,
                        Longitude = r.Longitude,
                        Activity = r.Activity,
                        Temperature = r.Temperature
                    });

                    if (batch.Count >= _opts.BatchSize)
                    {
                        await obsRepo.UpsertBatchAsync(batch, ct);
                        total += batch.Count;
                        batch.Clear();
                    }
                }

                pagesFetched++;
                if (!recent.Pagination.HasNextPage) break;
                page++;
            }

            if (batch.Count > 0)
            {
                await obsRepo.UpsertBatchAsync(batch, ct);
                total += batch.Count;
                batch.Clear();
            }

            await stateRepo.SetLastSyncAsync(esn, DateTime.UtcNow, ct);
        }
        return total;
    }
}


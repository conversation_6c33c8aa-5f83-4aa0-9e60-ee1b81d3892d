namespace CeresMockServer.Options;

public class DataGenOptions
{
    // Step granularity for generated observations
    public int StepSeconds { get; set; } = 60; // 1 minute

    // Movement speeds (meters/second)
    public double GrazingSpeedMps { get; set; } = 0.2;
    public double TravelingSpeedMps { get; set; } = 1.2;
    public double RestingSpeedMps { get; set; } = 0.0;

    // Behavior distribution (should sum ~1.0)
    public double GrazingProbability { get; set; } = 0.6;
    public double TravelingProbability { get; set; } = 0.2;
    public double RestingProbability { get; set; } = 0.2;

    // Default area center and radius (meters)
    public double DefaultCenterLat { get; set; } = -27.0;
    public double DefaultCenterLon { get; set; } = 151.0;
    public double PaddockRadiusMeters { get; set; } = 500.0;

    // Temperature baseline
    public double TemperatureMean { get; set; } = 22.0;
    public double TemperatureStdDev { get; set; } = 4.0;

    // Alert thresholds
    public double HighActivityThreshold { get; set; } = 0.7; // >70% walking in 30min
    public double NoActivityThreshold { get; set; } = 0.9;   // >90% resting in 60min
    public int LowBatteryThreshold { get; set; } = 20;       // <20%
}


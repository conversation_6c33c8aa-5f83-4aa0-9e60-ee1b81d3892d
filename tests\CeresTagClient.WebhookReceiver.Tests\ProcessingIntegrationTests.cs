using System.Net.Http.Headers;
using System.Net.Http.Json;
using CeresMockServer;
using CeresTagClient.Core.Dtos.Webhooks;
using CeresTagClient.Core.Interfaces;
using CeresTagClient.WebhookReceiver.Services;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using CeresMockServer.Services;

namespace CeresTagClient.WebhookReceiver.Tests;

public class ProcessingIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _mockFactory;

    public ProcessingIntegrationTests(WebApplicationFactory<Program> mockFactory)
    {
        _mockFactory = mockFactory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["CeresMock:Authentication:Issuer"] = "https://mock.cerestag.com",
                    ["CeresMock:Authentication:Audience"] = "ceres-api",
                    ["CeresMock:Authentication:SigningKey"] = "dev-super-secret-signing-key-change-me",
                    ["CeresMock:Authentication:TokenExpirySeconds"] = "7200",
                    ["ConnectionStrings:CeresDb"] = $"Data Source=proc-int-{Guid.NewGuid():N}.db"
                };
                config.AddInMemoryCollection(dict);
            });
        });
    }

    private WebApplicationFactory<CeresTagClient.WebhookReceiver.Program> CreateReceiverFactory(Action<IServiceCollection>? configureServices = null)
    {
        var factory = new WebApplicationFactory<CeresTagClient.WebhookReceiver.Program>().WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["WebhookReceiver:SharedSecret"] = "secretsecret",
                    ["CeresApi:BaseUrl"] = "http://localhost/"
                };
                config.AddInMemoryCollection(dict);
            });
            if (configureServices is not null)
            {
                builder.ConfigureServices(configureServices);
            }
        });
        return factory;
    }

    private async Task<(HttpClient mock, HttpClient receiver)> CreateClientsAsync(WebApplicationFactory<CeresTagClient.WebhookReceiver.Program> receiverFactory)
    {
        var mockClient = _mockFactory.CreateClient();
        // Acquire token via DI service for reliability
        var auth = _mockFactory.Services.GetRequiredService<CeresMockServer.Services.IAuthenticationService>();
        var tuple = await auth.TryGetTokenAsync("test-client-1", "test-secret-1", scope: "read write");
        Assert.True(tuple.Success);
        mockClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tuple.AccessToken);

        var receiverClient = receiverFactory.CreateClient(new WebApplicationFactoryClientOptions { AllowAutoRedirect = false });
        return (mockClient, receiverClient);
    }

    [Fact]
    public async Task Invalid_Signature_Is_Rejected()
    {
        var receiverFactory = CreateReceiverFactory();
        var (_, receiverClient) = await CreateClientsAsync(receiverFactory);
        var body = "{\"type\":\"alert\",\"version\":\"1.0\"}";
        var req = new HttpRequestMessage(HttpMethod.Post, "/api/webhooks/receive")
        {
            Content = new StringContent(body, System.Text.Encoding.UTF8, "application/json")
        };
        req.Headers.Add("X-Ceres-Signature", "sha256=deadbeef");
        var resp = await receiverClient.SendAsync(req);
        Assert.Equal(System.Net.HttpStatusCode.Unauthorized, resp.StatusCode);
    }

    [Theory]
    [InlineData("alert", typeof(AlertWebhookDto))]
    [InlineData("pfi", typeof(PfiWebhookDto))]
    [InlineData("historical", typeof(HistoricalWebhookDto))]
    [InlineData("standard", typeof(StandardWebhookDto))]
    public async Task Handlers_Are_Invoked_For_Types(string type, Type dtoType)
    {
        var invoked = new List<string>();
        var receiverFactory = CreateReceiverFactory(services =>
        {
            services.AddSingleton<IWebhookEventHandler<AlertWebhookDto>>(_ => new TestHandler<AlertWebhookDto>(invoked));
            services.AddSingleton<IWebhookEventHandler<PfiWebhookDto>>(_ => new TestHandler<PfiWebhookDto>(invoked));
            services.AddSingleton<IWebhookEventHandler<HistoricalWebhookDto>>(_ => new TestHandler<HistoricalWebhookDto>(invoked));
            services.AddSingleton<IWebhookEventHandler<StandardWebhookDto>>(_ => new TestHandler<StandardWebhookDto>(invoked));
        });
        var (mockClient, receiverClient) = await CreateClientsAsync(receiverFactory);

        // Create a signed payload via mock server
        var payloadFactory = _mockFactory.Services.GetRequiredService<CeresMockServer.Services.IWebhookPayloadFactory>();
        string payload = type switch
        {
            "alert" => payloadFactory.CreateAlert(),
            "pfi" => payloadFactory.CreatePfi(),
            "historical" => payloadFactory.CreateHistorical(),
            "standard" => payloadFactory.CreateStandard(),
            _ => "{}"
        };
        var signatureHex = CeresMockServer.Services.WebhookSigning.ComputeSignatureHex("secretsecret", payload);

        var req = new HttpRequestMessage(HttpMethod.Post, "/api/webhooks/receive")
        {
            Content = new StringContent(payload, System.Text.Encoding.UTF8, "application/json")
        };
        req.Headers.Add("X-Ceres-Signature", $"sha256={signatureHex}");

        var resp = await receiverClient.SendAsync(req);
        Assert.Equal(System.Net.HttpStatusCode.OK, resp.StatusCode);

        // Now let the background processor run
        var queue = receiverFactory.Services.GetRequiredService<IMessageQueue>();
        var router = receiverFactory.Services.GetRequiredService<IWebhookRouter>();
        queue.Enqueue(new InboundMessage(payload, DateTime.UtcNow));

        // Poll for handler invocation
        for (int i = 0; i < 50; i++)
        {
            if (invoked.Contains(type)) return;
            await Task.Delay(100);
        }
        Assert.Fail($"Handler for type {type} was not invoked");
    }

    [Fact]
    public async Task Failed_Handler_Goes_To_Dead_Letter()
    {
        var receiverFactory = CreateReceiverFactory(services =>
        {
            services.AddSingleton<IWebhookEventHandler<AlertWebhookDto>>(_ => new ThrowingHandler<AlertWebhookDto>());
        });
        var (mockClient, receiverClient) = await CreateClientsAsync(receiverFactory);

        var payloadFactory = _mockFactory.Services.GetRequiredService<CeresMockServer.Services.IWebhookPayloadFactory>();
        var payload = payloadFactory.CreateAlert();
        var signatureHex = CeresMockServer.Services.WebhookSigning.ComputeSignatureHex("secretsecret", payload);

        var req = new HttpRequestMessage(HttpMethod.Post, "/api/webhooks/receive")
        {
            Content = new StringContent(payload, System.Text.Encoding.UTF8, "application/json")
        };
        req.Headers.Add("X-Ceres-Signature", $"sha256={signatureHex}");
        var resp = await receiverClient.SendAsync(req);
        Assert.Equal(System.Net.HttpStatusCode.OK, resp.StatusCode);

        // Enqueue and allow background processing to attempt retries and then dead-letter
        var queue = receiverFactory.Services.GetRequiredService<IMessageQueue>();
        queue.Enqueue(new InboundMessage(payload, DateTime.UtcNow));

        var dead = receiverFactory.Services.GetRequiredService<CeresTagClient.WebhookReceiver.Services.IWebhookDeadLetterStore>();
        for (int i = 0; i < 100; i++)
        {
            if (dead.GetAll().Count > 0)
            {
                var dl = dead.GetAll().First();
                Assert.Equal("alert", dl.Type);
                Assert.True(dl.Attempts >= 1);
                Assert.False(string.IsNullOrWhiteSpace(dl.Reason));
                Assert.False(string.IsNullOrWhiteSpace(dl.Payload));
                return;
            }
            await Task.Delay(100);
        }
        Assert.Fail("Dead letter not created after failing handler");
    }

    private sealed class TestHandler<T> : IWebhookEventHandler<T>
    {
        private readonly List<string> _invoked;
        public TestHandler(List<string> invoked) { _invoked = invoked; }
        public Task HandleAsync(T payload, CancellationToken ct = default)
        {
            var type = payload switch
            {
                AlertWebhookDto => "alert",
                PfiWebhookDto => "pfi",
                HistoricalWebhookDto => "historical",
                StandardWebhookDto => "standard",
                _ => "unknown"
            };
            _invoked.Add(type);
            return Task.CompletedTask;
        }
    }

    private sealed class ThrowingHandler<T> : IWebhookEventHandler<T>
    {
        public Task HandleAsync(T payload, CancellationToken ct = default) => throw new InvalidOperationException("boom");
    }
}


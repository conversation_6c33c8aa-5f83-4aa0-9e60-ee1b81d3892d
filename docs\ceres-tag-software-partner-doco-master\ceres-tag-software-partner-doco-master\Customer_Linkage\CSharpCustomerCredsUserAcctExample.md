## Example C# Software Partner Customer Account Identifiers

## Overview

C # example on Software Partner Customer Account Identifiers

## Sample Code

```C#
// Author: <PERSON> (SatanEnglish)
// Date: 18th February 2021
// Description: C # example Code using Cognito authentication

public class ProccessUserCeresPortalCallback
{
	/// vars to be into example
	private IUserDatabaseService _userDatabaseService;
	private IFarmDatabaseService _farmDatabaseService;
	
	public ProccessUserCeresPortalCallback(IUserDatabaseService userDatabaseService, IFarmDatabaseService farmDatabaseService){
		_userDatabaseService = userDatabaseService;
		_farmDatabaseService = farmDatabaseService;
	}

	/// <summary>
    /// Example of expected userIdentifiers response from softwareVendor
    /// </summary>
    /// <returns>List of SoftwareAccountIdentifier for the give user</returns>
	[HttpGet]
	[Route("UserIdentifiers")]
	[Authorize]
	public ActionResult UserIdentifiers()
	{
		var results = new List<SoftwareAccountIdentifier>();
		var user = _userDatabaseService.GetUserByAuthSub(CognitoUserSub);
		if(user != null){
			var farms = _farmDatabaseService.GetUsersFarms(user);
			foreach(var farm in farms){
				result.Add(new SoftwareAccountIdentifier(){
					DisplayName = farm.Name,
					Identifier = farm.UniqueIdentifer //this value is returned as an identifier when you query the TagDetails endpoint
				});
			}
		}
		
		return results;
	}
	
	
	
	public string CognitoUserSub
	{
		get
		{
			var user = HttpContext.User;
			if (user != null && user.Claims.Any())
			{
				var sub = user.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier); 
				return sub.Value;
			}
			return null;
		}
	} 
}

public class SoftwareAccountIdentifier
{
    public string DisplayName { get; set; }
    public string Identifier { get; set; }
}
```

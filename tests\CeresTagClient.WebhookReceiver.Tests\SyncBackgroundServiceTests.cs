using CeresTagClient.Application.Sync;
using CeresTagClient.Core.Options;
using CeresTagClient.WebhookReceiver.Services;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;

namespace CeresTagClient.WebhookReceiver.Tests;

public class SyncBackgroundServiceTests : IClassFixture<WebApplicationFactory<CeresTagClient.WebhookReceiver.Program>>
{
    private readonly WebApplicationFactory<CeresTagClient.WebhookReceiver.Program> _factory;

    public SyncBackgroundServiceTests(WebApplicationFactory<CeresTagClient.WebhookReceiver.Program> factory)
    {
        _factory = factory;
    }

    private sealed class TestOrchestrator : ISyncOrchestrator
    {
        private readonly Action _onRun;
        public int Runs { get; private set; }
        public TestOrchestrator(Action onRun) { _onRun = onRun; }
        public Task<int> RunOnceAsync(CancellationToken ct = default)
        {
            Runs++;
            _onRun();
            return Task.FromResult(0);
        }
    }

    [Fact]
    public async Task DisabledSync_DoesNotInvokeOrchestrator()
    {
        var ran = 0;
        var factory = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, cfg) =>
            {
                cfg.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["Sync:Enabled"] = "false",
                    ["Sync:IntervalSeconds"] = "1",
                    ["CeresApi:BaseUrl"] = "http://localhost",
                    ["CeresApi:TimeoutSeconds"] = "5"
                });
            });
            builder.ConfigureServices(svcs =>
            {
                svcs.AddSingleton<ISyncOrchestrator>(sp => new TestOrchestrator(() => Interlocked.Increment(ref ran)));
            });
        });

        // Start host to allow hosted service to run briefly
        using var client = factory.CreateClient();
        await Task.Delay(800); // small delay to give hosted service a tick

        Assert.Equal(0, ran);
    }

    [Fact]
    public async Task EnabledSync_InvokesOrchestrator()
    {
        var ran = 0;
        var factory = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, cfg) =>
            {
                cfg.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["Sync:Enabled"] = "true",
                    ["Sync:IntervalSeconds"] = "1",
                    ["Sync:Esns:0"] = "100000000000000",
                    ["CeresApi:BaseUrl"] = "http://localhost",
                    ["CeresApi:TimeoutSeconds"] = "5"
                });
            });
            builder.ConfigureServices(svcs =>
            {
                svcs.AddSingleton<ISyncOrchestrator>(sp => new TestOrchestrator(() => Interlocked.Increment(ref ran)));
            });
        });

        using var client = factory.CreateClient();
        // Wait up to 3 seconds for at least one tick
        var sw = System.Diagnostics.Stopwatch.StartNew();
        while (sw.Elapsed < TimeSpan.FromSeconds(3))
        {
            if (Volatile.Read(ref ran) > 0) break;
            await Task.Delay(100);
        }
        Assert.True(ran > 0, "Orchestrator should have been called when Sync.Enabled = true");
    }
}


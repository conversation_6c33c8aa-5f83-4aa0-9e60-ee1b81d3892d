import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>eist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Logo from "./logo";
import Providers from "./providers";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "CeresTag Live",
  description: "Live tag map, battery health, alerts, and historical playback",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased bg-[#0B1020] text-[#F7F8FA]`}>
        <div className="min-h-screen flex flex-col">
          <header className="h-14 border-b border-white/10 flex items-center px-4 gap-3">
            <Logo />
            <div className="font-semibold tracking-wide">CeresTag Live</div>
          </header>
          <main className="flex-1"><Providers>{children}</Providers></main>
        </div>
      </body>
    </html>
  );
}

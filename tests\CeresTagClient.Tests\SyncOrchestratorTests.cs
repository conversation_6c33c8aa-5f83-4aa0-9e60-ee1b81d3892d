using CeresTagClient.Application.Sync;
using CeresTagClient.Core.Dtos;
using CeresTagClient.Core.Interfaces;
using CeresTagClient.Core.Options;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;

namespace CeresTagClient.Tests;

public class SyncOrchestratorTests
{
    private class StubTagService : ITagService
    {
        private readonly Dictionary<(string esn, int page), List<RecentObservation>> _data = new();

        public void Seed(string esn, IEnumerable<RecentObservation> observations, int pageSize)
        {
            int i = 0; var page = 1; var list = new List<RecentObservation>();
            foreach (var o in observations.OrderBy(x => x.Timestamp))
            {
                list.Add(o);
                i++;
                if (i % pageSize == 0)
                {
                    _data[(esn, page)] = new List<RecentObservation>(list);
                    list.Clear();
                    page++;
                }
            }
            if (list.Count > 0) _data[(esn, page)] = new List<RecentObservation>(list);
        }

        public Task<FirstContactResponse> FirstContactAsync(CancellationToken ct = default) => Task.FromResult(new FirstContactResponse());
        public Task<TagDetailsResponse> GetTagDetailsAsync(string esn, CancellationToken ct = default) => Task.FromResult(new TagDetailsResponse());
        public Task<HistoricalRequestResponse> RequestHistoricalDataAsync(string esn, DateTime startDate, DateTime endDate, CancellationToken ct = default) => Task.FromResult(new HistoricalRequestResponse());
        public Task<HistoricalRetrieveResponse> GetHistoricalDataAsync(string esn, string requestId, int pageSize = 100, int pageNumber = 1, CancellationToken ct = default) => Task.FromResult(new HistoricalRetrieveResponse());

        public Task<RecentTagDataResponse> GetRecentTagDataAsync(string esn, DateTime? fromDate = null, int pageSize = 100, int pageNumber = 1, CancellationToken ct = default)
        {
            _data.TryGetValue((esn, pageNumber), out var list);
            return Task.FromResult(new RecentTagDataResponse
            {
                Data = list ?? new List<RecentObservation>(),
                Pagination = new PaginationMeta
                {
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    HasNextPage = pageNumber < _data.Keys.Where(k => k.esn == esn).Select(k => k.page).DefaultIfEmpty(0).Max()
                }
            });
        }
    }

    [Fact]
    public async Task RunOnce_Batches_And_Updates_State()
    {
        var opts = Options.Create(new SyncOptions
        {
            Enabled = true,
            IntervalSeconds = 10,
            BatchSize = 50,
            PageSize = 20,
            MaxPagesPerRun = 10,
            Esns = new List<string> { "ESN001" },
            InitialLookbackHours = 1
        });

        var stateRepo = new InMemorySyncStateRepository();
        var obsRepo = new InMemoryObservationRepository();
        var tagService = new StubTagService();

        var start = DateTime.UtcNow.AddMinutes(-30);
        var observations = Enumerable.Range(0, 123).Select(i => new RecentObservation
        {
            Esn = "ESN001",
            Timestamp = start.AddMinutes(i),
            Latitude = -33.0,
            Longitude = 151.0,
            Activity = i % 2 == 0 ? "walking" : "grazing",
            Temperature = 25.0
        });
        tagService.Seed("ESN001", observations, opts.Value.PageSize);

        var sut = new SyncOrchestrator(tagService, stateRepo, obsRepo, opts, NullLogger<SyncOrchestrator>.Instance);
        var total = await sut.RunOnceAsync();

        Assert.Equal(123, total);
        Assert.True((await stateRepo.GetLastSyncAsync("ESN001")) != null);
        Assert.True(obsRepo.CountFor("ESN001") >= 123);
    }
}


using System;
using System.Net;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using CeresTagClient.Application.Services;
using CeresTagClient.Application.Validation;
using CeresTagClient.Core.Dtos;
using CeresTagClient.Core.Interfaces;
using CeresTagClient.Core.Requests;
using FluentValidation;
using Xunit;

namespace CeresTagClient.Tests;

public class InterfaceNamingExtensionsTests
{
    private static ITagService CreateTagService(Func<HttpRequestMessage, Task<HttpResponseMessage>> responder)
    {
        var httpHandler = new StubHttpHandler(responder);
        var pipeline = new StubAuthHandler("test-token") { InnerHandler = httpHandler };
        var httpClient = new HttpClient(pipeline) { BaseAddress = new Uri("https://mock") };
        var factory = new StubFactory(httpClient);

        IValidator<TagDetailsRequest> tagDetailsValidator = new TagDetailsRequestValidator();
        IValidator<RecentTagDataRequest> recentValidator = new RecentTagDataRequestValidator();
        IValidator<HistoricalRequest> historicalValidator = new HistoricalRequestValidator();
        IValidator<HistoricalRetrieveRequest> historicalRetrieveValidator = new HistoricalRetrieveRequestValidator();
        return new TagService(factory, tagDetailsValidator, recentValidator, historicalValidator, historicalRetrieveValidator);
    }

    [Fact]
    public async Task TagService_Extensions_Forward_To_Underlying_Methods()
    {
        var sut = CreateTagService(async req =>
        {
            if (req.Method == HttpMethod.Get && req.RequestUri!.AbsolutePath.Contains("/recent"))
                return new HttpResponseMessage(HttpStatusCode.OK) { Content = new StringContent(JsonSerializer.Serialize(new RecentTagDataResponse()), Encoding.UTF8, "application/json") };
            if (req.Method == HttpMethod.Post && req.RequestUri!.AbsolutePath.Contains("/historical/request"))
                return new HttpResponseMessage(HttpStatusCode.OK) { Content = new StringContent(JsonSerializer.Serialize(new HistoricalRequestResponse { RequestId = "req" }), Encoding.UTF8, "application/json") };
            if (req.Method == HttpMethod.Get && req.RequestUri!.AbsolutePath.Contains("/historical/"))
                return new HttpResponseMessage(HttpStatusCode.OK) { Content = new StringContent(JsonSerializer.Serialize(new HistoricalRetrieveResponse()), Encoding.UTF8, "application/json") };
            return new HttpResponseMessage(HttpStatusCode.NotFound);
        });

        _ = await sut.GetRecentDataAsync("100000000000000");
        _ = await sut.RequestHistoricalAsync("100000000000000", DateTime.UtcNow.AddHours(-1), DateTime.UtcNow);
        _ = await sut.RetrieveHistoricalDataAsync("100000000000000", "req");
    }

    private class StubAuthHandler(string token) : DelegatingHandler
    {
        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            return base.SendAsync(request, cancellationToken);
        }
    }

    private class StubHttpHandler(Func<HttpRequestMessage, Task<HttpResponseMessage>> responder) : HttpMessageHandler
    {
        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
            => responder(request);
    }

    private class StubFactory(HttpClient client) : IHttpClientFactory
    {
        public HttpClient CreateClient(string name) => client;
    }
}


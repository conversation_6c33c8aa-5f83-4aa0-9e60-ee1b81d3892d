# Example API Code in Python 2.7



## Overview

Python 2.7 example Code to authenticate and connect to the test API endpoint to get tag details every hour.
## Dependencies

The code runs on Python 2.7 and dependencies are: requests, Base64, json, datetime, os, time

## Sample Code

```Python
"""
Author: <PERSON><PERSON> <PERSON> (dnphilpot)
Date: 20th November 2020
Description: Python 2.7 example Code to authenticate and connect to the test API endpoint to get tag details every hour.
"""

import requests, base64
import json
import datetime
import os
import time

G_APIURL = 'https://extapi.test.cerestag.com'
G_ID = '<YOUR ID FROM CERES>'
G_SECRET = '<YOUR SECRET FROM CERES>'


def Login():
    global G_ID
    global G_SECRET

    print('Getting new access token...')

    url = 'https://testcerestag.auth.ap-southeast-2.amazoncognito.com/oauth2/token?audience=bar'
    usrPass = G_ID + ':' + G_SECRET
    b64Val = base64.b64encode(usrPass)

    payload = {'grant_type': 'client_credentials', 'scope': 'https://www.cerestag.com/ctms/tags.read'}

    r = requests.post(url,
                    headers={"Authorization": "Basic %s" % b64Val},
                    data=payload)

    content_json = json.loads(r.content)
    access_token = content_json['access_token']
    expires_in = content_json['expires_in']
    token_type = content_json['token_type']

    print('access_token: {}'.format(access_token))
    print('expires_in: {}'.format(expires_in))
    print('token_type: {}'.format(token_type))

    return access_token, expires_in, token_type

def CheckAuthentication(access_token):
    global G_APIURL

    url = G_APIURL + '/FirstContact/CheckAuthentication'
    r = requests.get(url,
                    headers={
                        "Authorization": "Bearer {}".format(access_token),
                        "accept": "text/plain",
                    })
    print(r.content)

def SigninCallback(access_token):
    global G_APIURL

    url = G_APIURL + '/SoftwareVenderUserAuthenicationCallBack/SigninCallback'
    r = requests.get(url,
                    headers={
                        "Authorization": "Bearer {}".format(access_token),
                        "accept": "*/*",
                    })
    print(r.content)
    return r.content

def GetAnimalObservationsSince(access_token):
    data_folder = 'data'
    unprocessed_packets_folder = os.path.join(data_folder, 'unprocessed_packets')
    last_dt_filename = os.path.join(data_folder, 'API_last_fetch_dt.txt')
    print('Getting observations: {}'.format(datetime.datetime.now()))

    if not os.path.exists(data_folder):
        try:
            os.makedirs(data_folder)
        except:
            pass
    if not os.path.exists(unprocessed_packets_folder):
        try:
            os.makedirs(unprocessed_packets_folder)
        except:
            pass

    # GET LAST DT RECEIVED
    try:
        with open(last_dt_filename, 'r') as f:
            from_dt_str = f.read()
            from_dt_str = from_dt_str.strip()

            # don't want it to be exactly the same as the last one otherwise you get duplicate entries
            from_dt = datetime.datetime.strptime(from_dt_str, '%Y-%m-%dT%H:%M:%SZ')
            from_dt += datetime.timedelta(seconds=1)
            from_dt_str = from_dt.strftime('%Y-%m-%dT%H:%M:%SZ')
    except:
        # if something goes wrong (e.g. the file doesn't exist) default to last 12 hours
        from_dt = datetime.datetime.utcnow() - datetime.timedelta(hours=12)
        from_dt_str = from_dt.strftime('%Y-%m-%dT%H:%M:%SZ')
    print('from_dt: {}'.format(from_dt_str))
    from_dt_str = from_dt_str.replace(':', '%3A')

    # GET THE DATA
    url = G_APIURL + '/RecentTagData/GetAnimalObservationsSince?fromDate=' + from_dt_str
    r = requests.get(url,
                    headers={
                        "Authorization": "Bearer {}".format(access_token),
                        "accept": "text/plain",
                    })
    try:
        observations = json.loads(r.content)
    except:
        return False   # this can happen when the token has expired.

    latest_dt_str = None

    # WORK OUT LATEST DATETIME OF DATA RECEIVED FOR THIS OBSERVATION
    for observation in observations['observations']:
        observationDateUTC = observation['observationDateUTC']
        if latest_dt_str is None:
            latest_dt_str = observationDateUTC
        elif observationDateUTC > latest_dt_str:
            latest_dt_str = observationDateUTC
        elif observationDateUTC < latest_dt_str:
            assert(False)   # the data received is NOT in date order

    # write latest datetime (so know where to start next time)
    if latest_dt_str is not None:
        print('new from_dt: {}'.format(latest_dt_str))
        with open(last_dt_filename, 'w') as f:
            f.write(latest_dt_str)

        # write unprocessed packet
        dt_now = datetime.datetime.now()
        dt_now_str = dt_now.strftime('%Y%m%d_%H%M%S')
        filename = os.path.join(unprocessed_packets_folder, dt_now_str + '_api.json')
        with open(filename, 'w') as packet_file:
            packet_file.write(r.content)

    return True

def GetTagDetailsSince(access_token):  # not really sure what this is for
    global G_APIURL
    url = G_APIURL + '/TagDetails/GetTagDetailsSince'
    r = requests.get(url,
                    headers={
                        "Authorization": "Bearer {}".format(access_token),
                        "accept": "text/plain",
                    })
    tags_json = json.loads(r.content)
    print(tags_json)
    with open('tag_details.json', 'w') as f:
        f.write(r.content)
    return tags_json

# Initialise the access_token
access_token, expires_in, token_type = Login()

while True:
    result = GetAnimalObservationsSince(access_token=access_token)
    if not result:  # renew access token
        access_token, expires_in, token_type = Login()
    time.sleep(3600)  # wait for 1 hour
```

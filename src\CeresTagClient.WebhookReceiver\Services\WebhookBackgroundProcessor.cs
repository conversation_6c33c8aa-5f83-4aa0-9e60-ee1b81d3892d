using Microsoft.Extensions.Hosting;

namespace CeresTagClient.WebhookReceiver.Services;

public class WebhookBackgroundProcessor(IMessageQueue queue, IWebhookRouter router, IWebhookDeadLetterStore deadLetters, ILogger<WebhookBackgroundProcessor> logger) : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                if (!queue.TryDequeue(out var msg) || msg is null)
                {
                    await Task.Delay(100, stoppingToken);
                    continue;
                }

                // Determine type for routing
                if (!router.TryGetType(msg.Payload, out var type) || !router.TryResolveHandler(type, out var handler))
                {
                    logger.LogWarning("No handler registered or type missing for payload");
                    continue;
                }

                var attempts = 0;
                var delay = TimeSpan.FromMilliseconds(200);
                for (;;)
                {
                    try
                    {
                        attempts++;
                        await handler.HandleAsync(msg.Payload, stoppingToken);
                        break; // success
                    }
                    catch (OperationCanceledException)
                    {
                        throw;
                    }
                    catch (Exception ex)
                    {
                        if (attempts >= 5)
                        {
                            deadLetters.Add(new ClientDeadLetter
                            {
                                TimestampUtc = DateTime.UtcNow,
                                Type = type,
                                Attempts = attempts,
                                Reason = ex.Message,
                                Payload = msg.Payload
                            });
                            logger.LogError(ex, "Webhook processing failed after {Attempts} attempts for type {Type}", attempts, type);
                            break;
                        }
                        await Task.Delay(delay, stoppingToken);
                        delay = TimeSpan.FromMilliseconds((int)(delay.TotalMilliseconds * 2 + Random.Shared.Next(50, 150)));
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // shutdown
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Unhandled exception in webhook background processor");
            }
        }
    }
}


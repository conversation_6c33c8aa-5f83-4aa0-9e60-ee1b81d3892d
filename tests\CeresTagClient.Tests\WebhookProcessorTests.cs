using System.Text.Json;
using CeresTagClient.Application.Webhooks;
using CeresTagClient.Core.Dtos.Webhooks;

namespace CeresTagClient.Tests;

public class WebhookProcessorTests
{
    private class DummyFactory : IHttpClientFactory
    {
        public HttpClient CreateClient(string name) => new HttpClient { BaseAddress = new Uri("http://localhost") };
    }

    [Theory]
    [InlineData("alert", "{\"type\":\"alert\",\"version\":\"1.0\",\"alert_type\":\"high_activity\",\"esn\":\"100\",\"timestamp\":\"2020-01-01T00:00:00Z\",\"severity\":\"warning\"}")]
    [InlineData("pfi", "{\"type\":\"pfi\",\"version\":\"1.0\",\"date\":\"2020-01-01\",\"pfi\":{\"score\":0.9,\"herd\":0.8,\"individual\":0.95}}")]
    [InlineData("historical", "{\"type\":\"historical\",\"version\":\"1.0\",\"request_id\":\"abc\",\"status\":\"ready\"}")]
    [InlineData("standard", "{\"type\":\"standard\",\"version\":\"1.0\",\"ts\":\"2020-01-01T00:00:00Z\",\"data\":[{\"esn\":\"100\",\"lat\":-27,\"lon\":152}]}")]
    public async Task Processor_Accepts_Version_1_0(string type, string json)
    {
        var processor = new WebhookProcessor(new DummyFactory());
        if (type == "alert") await processor.ProcessAlertAsync(JsonSerializer.Deserialize<AlertWebhookDto>(json)!);
        else if (type == "pfi") await processor.ProcessPfiAsync(JsonSerializer.Deserialize<PfiWebhookDto>(json)!);
        else if (type == "historical") await processor.ProcessHistoricalAsync(JsonSerializer.Deserialize<HistoricalWebhookDto>(json)!);
        else await processor.ProcessStandardAsync(JsonSerializer.Deserialize<StandardWebhookDto>(json)!);
    }

    [Theory]
    [InlineData("alert", "{\"type\":\"alert\",\"version\":\"2.0\"}")]
    [InlineData("pfi", "{\"type\":\"pfi\",\"version\":\"2.0\"}")]
    public async Task Processor_Rejects_Unsupported_Version(string type, string json)
    {
        var processor = new WebhookProcessor(new DummyFactory());
        await Assert.ThrowsAsync<InvalidOperationException>(async () =>
        {
            if (type == "alert") await processor.ProcessAlertAsync(JsonSerializer.Deserialize<AlertWebhookDto>(json)!);
            else await processor.ProcessPfiAsync(JsonSerializer.Deserialize<PfiWebhookDto>(json)!);
        });
    }
}


using System.IdentityModel.Tokens.Jwt;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using CeresMockServer;
using CeresMockServer.Models;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;

namespace CeresMockServer.Tests;

public class CustomerLinkageTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public CustomerLinkageTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((ctx, config) =>
            {
                var dict = new Dictionary<string, string?>
                {
                    ["ConnectionStrings:CeresDb"] = $"Data Source=customer-link-{Guid.NewGuid():N}.db",
                    ["CeresMock:CustomerAuth:SigningKey"] = "test-customer-signing-key-0123456789-0123456789",
                    ["CeresMock:CustomerAuth:Issuer"] = "https://mock.cerestag.com/customer",
                    ["CeresMock:CustomerAuth:Audience"] = "ceres-customer",
                    ["CeresMock:CustomerAuth:TokenExpirySeconds"] = "300",
                    ["CeresMock:CustomerAuth:DefaultRedirectUri"] = "http://localhost/callback"
                };
                config.AddInMemoryCollection(dict);
            });
        });
    }

    [Fact]
    public async Task Customer_Authorization_Code_Flow_EndToEnd()
    {
        var client = _factory.CreateClient();
        var token = AuthTestHelper.CreateTestToken(_factory.Services.GetRequiredService<IConfiguration>());
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        // Start auth
        var startReq = new CustomerAuthStartRequest { RedirectUri = "http://localhost/callback", State = "abc123" };
        var startResp = await client.PostAsJsonAsync("/api/v1/customer-linkage/start", startReq);
        Assert.Equal(HttpStatusCode.OK, startResp.StatusCode);
        var startPayload = await startResp.Content.ReadFromJsonAsync<CustomerAuthStartResponse>();
        Assert.NotNull(startPayload);
        Assert.Contains("code=", startPayload!.AuthorizationUrl);
        Assert.Contains("state=abc123", startPayload!.AuthorizationUrl);

        // Extract code from URL
        var uri = new Uri(startPayload.AuthorizationUrl);
        var q = System.Web.HttpUtility.ParseQueryString(uri.Query);
        var code = q.Get("code")!;

        // Exchange code for customer token
        var cbResp = await client.PostAsJsonAsync("/api/v1/customer-linkage/callback", new CustomerAuthCallbackRequest { Code = code, State = "abc123" });
        Assert.Equal(HttpStatusCode.OK, cbResp.StatusCode);
        var tokenPayload = await cbResp.Content.ReadFromJsonAsync<CustomerTokenResponse>();
        Assert.NotNull(tokenPayload);
        Assert.False(string.IsNullOrEmpty(tokenPayload!.AccessToken));
        Assert.False(string.IsNullOrEmpty(tokenPayload!.CustomerId));

        // Validate token structure (signing key value was overridden in config)
        var handler = new JwtSecurityTokenHandler();
        var jwt = handler.ReadJwtToken(tokenPayload.AccessToken);
        Assert.Equal("customer", jwt.Claims.First(c => c.Type == "scope").Value);
        Assert.Equal(tokenPayload.CustomerId, jwt.Claims.First(c => c.Type == "customer_id").Value);

        // Link a property
        var linkReq = new LinkPropertyRequest { CustomerId = tokenPayload.CustomerId, PropertyId = "prop-xyz", PropertyName = "XYZ Farm" };
        var linkCreate = await client.PostAsJsonAsync("/api/v1/customer-linkage/links", linkReq);
        Assert.Equal(HttpStatusCode.Created, linkCreate.StatusCode);
        var linkObj = await linkCreate.Content.ReadFromJsonAsync<CustomerLinkResponse>();
        Assert.NotNull(linkObj);
        Assert.True(linkObj!.Id > 0);

        // Fetch links
        var getLinks = await client.GetAsync($"/api/v1/customer-linkage/links/{tokenPayload.CustomerId}");
        Assert.Equal(HttpStatusCode.OK, getLinks.StatusCode);
        var linkList = await getLinks.Content.ReadFromJsonAsync<List<CustomerLinkResponse>>();
        Assert.NotNull(linkList);
        Assert.Single(linkList!);

        // De-link
        var del = await client.DeleteAsync($"/api/v1/customer-linkage/links/{linkObj.Id}");
        Assert.Equal(HttpStatusCode.NoContent, del.StatusCode);

        // Confirm gone
        var getLinks2 = await client.GetAsync($"/api/v1/customer-linkage/links/{tokenPayload.CustomerId}");
        var linkList2 = await getLinks2.Content.ReadFromJsonAsync<List<CustomerLinkResponse>>();
        Assert.NotNull(linkList2);
        Assert.Empty(linkList2!);
    }
}


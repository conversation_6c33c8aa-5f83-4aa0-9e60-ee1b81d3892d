using System.Collections.Concurrent;

namespace CeresMockServer.Services;

public class WebhookDeadLetter
{
    public DateTime TimestampUtc { get; init; } = DateTime.UtcNow;
    public string Url { get; init; } = string.Empty;
    public string Type { get; init; } = string.Empty;
    public int Attempts { get; init; }
    public string Reason { get; init; } = string.Empty;
    public Dictionary<string, string> Headers { get; init; } = new();
    public string Payload { get; init; } = string.Empty;
}

public interface IWebhookDeadLetterStore
{
    void Add(WebhookDeadLetter entry);
    IReadOnlyList<WebhookDeadLetter> GetAll();
    void Clear();
}

public class InMemoryWebhookDeadLetterStore : IWebhookDeadLetterStore
{
    private readonly ConcurrentQueue<WebhookDeadLetter> _queue = new();
    private readonly int _capacity;

    public InMemoryWebhookDeadLetterStore(int capacity = 500) => _capacity = Math.Max(1, capacity);

    public void Add(WebhookDeadLetter entry)
    {
        _queue.Enqueue(entry);
        while (_queue.Count > _capacity && _queue.TryDequeue(out _)) { }
    }

    public IReadOnlyList<WebhookDeadLetter> GetAll()
    {
        return _queue.ToArray().OrderByDescending(x => x.TimestampUtc).ToList();
    }

    public void Clear()
    {
        while (_queue.TryDequeue(out _)) { }
    }
}


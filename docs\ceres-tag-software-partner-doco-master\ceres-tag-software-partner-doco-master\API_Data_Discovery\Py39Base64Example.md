# How to Base 64 Encode API Client_ID & Secret



## Dependencies

The code runs on Python 3.9 and dependencies are: base64,

## Sample Code

```python
"""
Author: <PERSON>
Date: 7th April 2022
Description: Base64 encode Client_ID & Secret for use in Postman
Command to run in terminal: python <filename>.py client_id secret
"""
import base64
import sys
import readchar

print("ClientID: "+sys.argv[1]+"\nSecret: "+sys.argv[2]+"\n\n")
print("Enter = confirms input is correct, Q = quit script")
char = readchar.readkey()
if char == "\r":
	clientid = sys.argv[1]
	secret = sys.argv[2]
	userpass_string = clientid + ':' + secret
	userpass_bytes = userpass_string.encode("ascii")
	result_encoded = base64.b64encode(userpass_bytes)
	result_string = result_encoded.decode("ascii")
	print (result_string)
else:
	print("Quitting...")
	sys.exit(0)

```
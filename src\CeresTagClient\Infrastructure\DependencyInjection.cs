using System.Net.Http;
using CeresTagClient.Application.Services;
using CeresTagClient.Core.Interfaces;
using CeresTagClient.Core.Options;
using CeresTagClient.Infrastructure.Authentication;
using Microsoft.Extensions.Configuration;
using CeresTagClient.Core.Dtos.Webhooks;
using CeresTagClient.Core.Interfaces;
using Microsoft.Extensions.DependencyInjection.Extensions;

using CeresTagClient.Application.Webhooks;

using Microsoft.Extensions.DependencyInjection;
using Polly;
using Polly.Extensions.Http;

namespace CeresTagClient.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddCeresTagClient(this IServiceCollection services, IConfiguration config)
    {
        services.Configure<CeresApiOptions>(config.GetSection("CeresApi"));

        services.AddTransient<AuthDelegatingHandler>();

        // Polly policies
        var retryPolicy = HttpPolicyExtensions
            .HandleTransientHttpError()
            .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));

        var breakerPolicy = HttpPolicyExtensions
            .HandleTransientHttpError()
            .CircuitBreakerAsync(5, TimeSpan.FromSeconds(30));

        services.AddHttpClient<IAuthenticationService, AuthenticationService>((sp, client) =>
        {
            var opts = sp.GetRequiredService<Microsoft.Extensions.Options.IOptions<CeresApiOptions>>().Value;
            client.BaseAddress = new Uri(opts.BaseUrl);
            client.Timeout = TimeSpan.FromSeconds(opts.TimeoutSeconds);
        })
        .AddPolicyHandler(retryPolicy)
        .AddPolicyHandler(breakerPolicy);

        services.AddHttpClient("CeresApi", (sp, client) =>
        {
            var opts = sp.GetRequiredService<Microsoft.Extensions.Options.IOptions<CeresApiOptions>>().Value;
            client.BaseAddress = new Uri(opts.BaseUrl);
            client.Timeout = TimeSpan.FromSeconds(opts.TimeoutSeconds);
        })
        .AddHttpMessageHandler<AuthDelegatingHandler>()
        .AddPolicyHandler(retryPolicy)
        .AddPolicyHandler(breakerPolicy);

        services.AddScoped<ITagService, TagService>();
        services.AddScoped<IHistoricalDataService, HistoricalDataService>();
        services.AddScoped<ITransferService, TransferService>();
        services.AddScoped<IPropertyService, PropertyService>();
        services.AddScoped<ICustomerService, CustomerService>();
        services.AddScoped<IWebhookSignatureValidator, CeresTagClient.Application.Webhooks.HmacSha256SignatureValidator>();
        // Register webhook processor via explicit factory to avoid constructor ambiguity and allow event-driven fan-out
        services.AddSingleton<IWebhookProcessor>(sp =>
        {
            var alerts = sp.GetServices<IWebhookEventHandler<AlertWebhookDto>>();
            var pfi = sp.GetServices<IWebhookEventHandler<PfiWebhookDto>>();
            var hist = sp.GetServices<IWebhookEventHandler<HistoricalWebhookDto>>();
            var std = sp.GetServices<IWebhookEventHandler<StandardWebhookDto>>();
            return new CeresTagClient.Application.Webhooks.WebhookProcessor(alerts, pfi, hist, std);
        });
        // Event handlers - consumers can register their own; provide no-op defaults for tests
        services.TryAddEnumerable(ServiceDescriptor.Singleton<IWebhookEventHandler<AlertWebhookDto>, NoopAlertHandler>());
        services.TryAddEnumerable(ServiceDescriptor.Singleton<IWebhookEventHandler<PfiWebhookDto>, NoopPfiHandler>());
        services.TryAddEnumerable(ServiceDescriptor.Singleton<IWebhookEventHandler<HistoricalWebhookDto>, NoopHistoricalHandler>());
        services.TryAddEnumerable(ServiceDescriptor.Singleton<IWebhookEventHandler<StandardWebhookDto>, NoopStandardHandler>());

        // Sync options and services (disabled by default)
        services.Configure<CeresTagClient.Core.Options.SyncOptions>(config.GetSection("Sync"));
        services.AddSingleton<CeresTagClient.Core.Interfaces.ISyncStateRepository, CeresTagClient.Application.Sync.InMemorySyncStateRepository>();
        services.AddSingleton<CeresTagClient.Core.Interfaces.IObservationRepository, CeresTagClient.Application.Sync.InMemoryObservationRepository>();
        services.AddScoped<CeresTagClient.Application.Sync.ISyncOrchestrator, CeresTagClient.Application.Sync.SyncOrchestrator>();
        // Background service registration is done by host apps (e.g., WebhookReceiver), not the client library

        // Validators
        services.AddScoped<FluentValidation.IValidator<CeresTagClient.Core.Requests.TagDetailsRequest>, CeresTagClient.Application.Validation.TagDetailsRequestValidator>();
        services.AddScoped<FluentValidation.IValidator<CeresTagClient.Core.Requests.RecentTagDataRequest>, CeresTagClient.Application.Validation.RecentTagDataRequestValidator>();
        services.AddScoped<FluentValidation.IValidator<CeresTagClient.Core.Requests.HistoricalRequest>, CeresTagClient.Application.Validation.HistoricalRequestValidator>();
        services.AddScoped<FluentValidation.IValidator<CeresTagClient.Core.Requests.HistoricalRetrieveRequest>, CeresTagClient.Application.Validation.HistoricalRetrieveRequestValidator>();

        return services;
    }
}

